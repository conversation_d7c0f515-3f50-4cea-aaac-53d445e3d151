2025-07-20 09:30:18.829 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:30:18.984 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 09:30:18.998 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 09:30:22.943 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:30:23.078 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:30:23.082 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:30:23.089 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:30:23.091 +03:00 [INF] Hosting environment: Production
2025-07-20 09:30:23.092 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:30:40.314 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 09:30:40.315 +03:00 [INF] COM ports populated.
2025-07-20 09:30:41.456 +03:00 [INF] No barcode scanner detected.
2025-07-20 09:30:41.458 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-20 09:30:41.459 +03:00 [INF] TabPage returned successfully.
2025-07-20 09:30:41.653 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 09:30:55.288 +03:00 [INF] Total tabs in control: 5
2025-07-20 09:30:55.294 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 09:30:55.295 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 09:30:55.296 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 09:30:55.298 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 09:30:55.299 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 09:31:22.662 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:31:22.729 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 09:31:22.734 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 09:31:25.701 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:31:25.752 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:31:25.754 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:31:25.756 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:31:25.757 +03:00 [INF] Hosting environment: Production
2025-07-20 09:31:25.758 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:32:28.875 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:32:28.898 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 09:32:28.901 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 09:32:31.120 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:32:31.155 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:32:31.156 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:32:31.158 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:32:31.159 +03:00 [INF] Hosting environment: Production
2025-07-20 09:32:31.159 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:32:42.268 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:32:42.394 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 09:32:42.409 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 09:34:07.981 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:34:08.005 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 09:34:08.008 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 09:34:30.993 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:34:31.079 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 09:34:31.090 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 09:34:33.966 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:34:34.056 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:34:34.060 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:34:34.064 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:34:34.065 +03:00 [INF] Hosting environment: Production
2025-07-20 09:34:34.067 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:34:46.390 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 09:34:46.391 +03:00 [INF] COM ports populated.
2025-07-20 09:34:47.530 +03:00 [INF] No barcode scanner detected.
2025-07-20 09:34:47.536 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-20 09:34:47.538 +03:00 [INF] TabPage returned successfully.
2025-07-20 09:34:47.651 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 09:34:47.665 +03:00 [INF] Total tabs in control: 5
2025-07-20 09:34:47.669 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 09:34:47.670 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 09:34:47.670 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 09:34:47.671 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 09:34:47.671 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 09:35:48.284 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:35:48.313 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 09:35:48.316 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 09:35:48.323 +03:00 [INF] Capture image mode set to: false
2025-07-20 09:35:48.323 +03:00 [INF] Initializing hub connection...
2025-07-20 09:35:49.459 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:35:49.497 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:35:49.500 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:35:49.501 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:35:49.502 +03:00 [INF] Hosting environment: Production
2025-07-20 09:35:49.502 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:35:50.602 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:35:50.618 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:35:50.632 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:35:50.634 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 34.9863ms
2025-07-20 09:35:52.710 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=nZ1wGBpuB_HNnBYtIy4nNg - null null
2025-07-20 09:35:52.714 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 09:35:52.798 +03:00 [INF] Successfully connected to Hub
2025-07-20 09:35:52.800 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 09:35:58.289 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 09:35:58.290 +03:00 [INF] COM ports populated.
2025-07-20 09:35:58.313 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 09:35:58.314 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 09:35:58.314 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 09:35:58.315 +03:00 [INF] TabPage returned successfully.
2025-07-20 09:35:58.415 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 09:35:58.429 +03:00 [INF] Total tabs in control: 5
2025-07-20 09:35:58.433 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 09:35:58.433 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 09:35:58.434 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 09:35:58.434 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 09:35:58.435 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 09:36:47.655 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null null
2025-07-20 09:36:47.662 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:36:47.665 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 204 null null 9.7766ms
2025-07-20 09:36:47.672 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:36:47.675 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:36:47.677 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:36:47.679 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:36:47.682 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 10.2691ms
2025-07-20 09:36:48.039 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=w6OyUvfRyJ9GZQyXQs7KBA - null null
2025-07-20 09:36:48.045 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:36:48.046 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 09:38:35.443 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:38:35.446 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:38:35.447 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 3.7994ms
2025-07-20 09:38:35.451 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:38:35.453 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:38:35.453 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:38:35.463 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:38:35.615 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:38:37.536 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 09:38:37.598 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 1979.0107ms.
2025-07-20 09:38:37.602 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 09:38:37.606 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 2140.0043ms
2025-07-20 09:38:37.607 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:38:37.607 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 2156.2896ms
2025-07-20 09:39:30.256 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:39:30.333 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:39:30.335 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 78.9058ms
2025-07-20 09:39:30.339 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:39:30.347 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:39:30.347 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:39:30.348 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:39:30.358 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:39:30.857 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 09:39:30.913 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 553.6728ms.
2025-07-20 09:39:30.914 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 09:39:30.916 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 565.9892ms
2025-07-20 09:39:30.917 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:39:30.918 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 579.7803ms
2025-07-20 09:39:57.078 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:39:57.100 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 09:39:57.103 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 09:39:57.110 +03:00 [INF] Capture image mode set to: false
2025-07-20 09:39:57.110 +03:00 [INF] Initializing hub connection...
2025-07-20 09:39:58.168 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:39:58.208 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:39:58.209 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:39:58.211 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:39:58.211 +03:00 [INF] Hosting environment: Production
2025-07-20 09:39:58.212 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:39:59.318 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:39:59.333 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:39:59.344 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:39:59.346 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 31.0623ms
2025-07-20 09:40:01.482 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=dCCMO7vzjyc8dWx7tm3U4A - null null
2025-07-20 09:40:01.488 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 09:40:01.551 +03:00 [INF] Successfully connected to Hub
2025-07-20 09:40:01.553 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 09:40:14.815 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:40:14.819 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:40:14.821 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 5.8711ms
2025-07-20 09:40:14.824 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:40:14.826 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:40:14.827 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:40:14.837 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:40:15.019 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:40:15.080 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:40:15.604 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 09:40:15.608 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:40:15.654 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:40:15.657 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 633.8174ms.
2025-07-20 09:40:15.662 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 09:40:15.666 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 825.4827ms
2025-07-20 09:40:15.667 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:40:15.668 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 843.4396ms
2025-07-20 09:41:51.293 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:41:51.313 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 09:41:51.316 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 09:41:51.324 +03:00 [INF] Capture image mode set to: false
2025-07-20 09:41:51.325 +03:00 [INF] Initializing hub connection...
2025-07-20 09:41:52.413 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:41:52.451 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:41:52.454 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:41:52.455 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:41:52.456 +03:00 [INF] Hosting environment: Production
2025-07-20 09:41:52.457 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:41:53.532 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:41:53.548 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:41:53.559 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:41:53.561 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 30.9328ms
2025-07-20 09:41:55.644 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ha0CJfCZiFxB1H4TPUfdEg - null null
2025-07-20 09:41:55.652 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 09:41:55.702 +03:00 [INF] Successfully connected to Hub
2025-07-20 09:41:55.704 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 09:42:14.790 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:42:14.795 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:42:14.797 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.5567ms
2025-07-20 09:42:14.801 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:42:14.803 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:42:14.804 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:42:14.815 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:42:14.996 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:42:15.062 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:44:17.840 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:44:17.862 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 09:44:17.864 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 09:44:17.872 +03:00 [INF] Capture image mode set to: false
2025-07-20 09:44:17.873 +03:00 [INF] Initializing hub connection...
2025-07-20 09:44:18.866 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:44:18.900 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:44:18.901 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:44:18.903 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:44:18.903 +03:00 [INF] Hosting environment: Production
2025-07-20 09:44:18.904 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:44:20.081 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:44:20.099 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:44:20.114 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:44:20.116 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 38.1381ms
2025-07-20 09:44:22.193 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=jpHHjOFWZyOoUoqdZKCWZQ - null null
2025-07-20 09:44:22.201 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 09:44:22.272 +03:00 [INF] Successfully connected to Hub
2025-07-20 09:44:22.273 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 09:44:28.601 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:44:28.607 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:44:28.608 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 8.0024ms
2025-07-20 09:44:28.617 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:44:28.619 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:44:28.620 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:44:28.635 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:44:28.890 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:44:28.976 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:45:09.355 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:45:09.477 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 09:45:09.492 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 09:45:09.526 +03:00 [INF] Capture image mode set to: false
2025-07-20 09:45:09.530 +03:00 [INF] Initializing hub connection...
2025-07-20 09:45:12.344 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:45:12.426 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:45:12.429 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:45:12.433 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:45:12.434 +03:00 [INF] Hosting environment: Production
2025-07-20 09:45:12.437 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:45:12.714 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:45:12.763 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:45:12.796 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:45:12.805 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 106.4107ms
2025-07-20 09:45:14.986 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=mBEUTlLECn8_8zdlSN0Jxw - null null
2025-07-20 09:45:14.995 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 09:45:15.088 +03:00 [INF] Successfully connected to Hub
2025-07-20 09:45:15.092 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 09:45:25.261 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:45:25.266 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:45:25.267 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 5.8517ms
2025-07-20 09:45:25.274 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:45:25.276 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:45:25.277 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:45:25.287 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:45:25.452 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:45:25.535 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:46:15.268 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:46:15.306 +03:00 [ERR] An error occurred during printing: HTML content is invalid or incomplete.
2025-07-20 09:46:15.348 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:46:15.350 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:46:15.351 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 83.4561ms
2025-07-20 09:46:15.356 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:46:15.357 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-20 09:46:15.362 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:46:15.363 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=mBEUTlLECn8_8zdlSN0Jxw - 101 null null 60381.405ms
2025-07-20 09:46:15.363 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:46:15.366 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:46:15.375 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:46:15.384 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:46:15.408 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:46:15.412 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 49957.8715ms.
2025-07-20 09:46:15.421 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 09:46:15.430 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 50141.5054ms
2025-07-20 09:46:15.431 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:46:15.432 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 50162.0238ms
2025-07-20 09:46:17.979 +03:00 [ERR] An error occurred during printing: HTML content is invalid or incomplete.
2025-07-20 09:46:17.981 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:46:18.032 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:46:18.034 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 2656.4056ms.
2025-07-20 09:46:18.036 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 09:46:18.037 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 2670.6236ms
2025-07-20 09:46:18.038 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:46:18.039 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 2683.3545ms
2025-07-20 09:46:19.977 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:46:19.978 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:46:19.979 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:46:20.000 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 22.8399ms
2025-07-20 09:46:22.034 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=_BfiX_QbDAlsWG4p7sThEg - null null
2025-07-20 09:46:22.041 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 09:46:25.311 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:46:25.312 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:46:25.312 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 1.4885ms
2025-07-20 09:46:25.315 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:46:25.317 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:46:25.318 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:46:25.319 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:46:25.327 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:46:25.338 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:46:32.366 +03:00 [ERR] An error occurred during printing: HTML content is invalid or incomplete.
2025-07-20 09:46:32.368 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:46:32.428 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:46:32.430 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 7102.3999ms.
2025-07-20 09:46:32.431 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 09:46:32.433 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 7113.1125ms
2025-07-20 09:46:32.433 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:46:32.434 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 7119.7711ms
2025-07-20 09:47:21.612 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null null
2025-07-20 09:47:21.616 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:47:21.618 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 204 null null 5.8951ms
2025-07-20 09:47:21.625 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:47:21.629 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:47:21.631 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:47:21.633 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:47:21.634 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 9.4785ms
2025-07-20 09:47:22.041 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=DUJuYnq3mZkLeKsBA-aqRw - null null
2025-07-20 09:47:22.046 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:47:22.051 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 09:47:43.618 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:47:43.620 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:47:43.620 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.2855ms
2025-07-20 09:47:43.623 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:47:43.625 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:47:43.625 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:47:43.626 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:47:43.634 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:47:43.643 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:47:48.908 +03:00 [ERR] An error occurred during printing: HTML content is invalid or incomplete.
2025-07-20 09:47:48.910 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:47:48.966 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:47:48.967 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 5332.3294ms.
2025-07-20 09:47:48.969 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 09:47:48.970 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 5343.193ms
2025-07-20 09:47:48.971 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:47:48.972 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 5350.0042ms
2025-07-20 09:48:04.210 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 09:48:04.210 +03:00 [INF] COM ports populated.
2025-07-20 09:48:04.235 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 09:48:04.236 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 09:48:04.236 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 09:48:04.237 +03:00 [INF] TabPage returned successfully.
2025-07-20 09:48:04.336 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 09:48:04.350 +03:00 [INF] Total tabs in control: 5
2025-07-20 09:48:04.354 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 09:48:04.355 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 09:48:04.356 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 09:48:04.356 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 09:48:04.357 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 09:49:51.819 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:49:51.823 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:49:51.823 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 4.6156ms
2025-07-20 09:49:51.826 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:49:51.827 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:49:51.827 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:49:51.827 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:49:51.835 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:49:53.756 +03:00 [ERR] An error occurred during printing: HTML content is invalid or incomplete.
2025-07-20 09:49:53.820 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 1984.203ms.
2025-07-20 09:49:53.823 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 09:49:53.824 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 1995.8968ms
2025-07-20 09:49:53.825 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:49:53.826 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 2000.2158ms
2025-07-20 09:50:28.894 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:50:28.896 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:50:28.896 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.3703ms
2025-07-20 09:50:28.898 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:50:28.906 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:50:28.906 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:50:28.907 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:50:28.916 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:51:21.805 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-20 09:51:21.843 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=_BfiX_QbDAlsWG4p7sThEg - 101 null null 299829.2244ms
2025-07-20 09:51:27.057 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:53:13.721 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:53:13.722 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:55:26.156 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:55:26.176 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 09:55:26.178 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 09:55:26.185 +03:00 [INF] Capture image mode set to: false
2025-07-20 09:55:26.186 +03:00 [INF] Initializing hub connection...
2025-07-20 09:55:27.083 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:55:27.124 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:55:27.126 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:55:27.127 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:55:27.128 +03:00 [INF] Hosting environment: Production
2025-07-20 09:55:27.128 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:55:28.433 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:55:28.449 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:55:28.460 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:55:28.463 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 32.2474ms
2025-07-20 09:55:30.569 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=0f5U6hlEu4YEVIJe-SFphA - null null
2025-07-20 09:55:30.572 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 09:55:30.609 +03:00 [INF] Successfully connected to Hub
2025-07-20 09:55:30.610 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 09:55:38.838 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:55:38.843 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:55:38.845 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 6.4345ms
2025-07-20 09:55:38.849 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:55:38.850 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:55:38.851 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:55:38.860 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:55:39.078 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:55:39.112 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:55:58.632 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 09:55:58.637 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:55:58.688 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:55:58.692 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 19607.9911ms.
2025-07-20 09:55:58.700 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 09:55:58.706 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 19842.4203ms
2025-07-20 09:55:58.707 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:55:58.710 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 19862.0299ms
2025-07-20 09:56:04.687 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:56:04.688 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:56:04.689 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.2171ms
2025-07-20 09:56:04.691 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:56:04.693 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:56:04.693 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:56:04.694 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:56:04.705 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:56:04.711 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:56:50.263 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-20 09:56:50.266 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=0f5U6hlEu4YEVIJe-SFphA - 101 null null 79701.6358ms
2025-07-20 09:57:37.692 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:57:37.714 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 09:57:37.717 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 09:57:37.724 +03:00 [INF] Capture image mode set to: false
2025-07-20 09:57:37.725 +03:00 [INF] Initializing hub connection...
2025-07-20 09:57:38.622 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:57:38.659 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:57:38.661 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:57:38.662 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:57:38.663 +03:00 [INF] Hosting environment: Production
2025-07-20 09:57:38.663 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:57:39.968 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:57:39.996 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:57:40.012 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:57:40.017 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 56.3917ms
2025-07-20 09:57:42.117 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=m_PZd3QGVAonLOkOBOJpGQ - null null
2025-07-20 09:57:42.126 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 09:57:42.174 +03:00 [INF] Successfully connected to Hub
2025-07-20 09:57:42.176 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 09:57:43.942 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:57:43.950 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:57:43.952 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 10.2191ms
2025-07-20 09:57:43.963 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:57:43.978 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:57:43.980 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:57:43.998 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:57:44.257 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:57:44.336 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:57:59.717 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 09:57:59.802 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 09:57:59.812 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 09:57:59.833 +03:00 [INF] Capture image mode set to: false
2025-07-20 09:57:59.836 +03:00 [INF] Initializing hub connection...
2025-07-20 09:58:02.502 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 09:58:02.581 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 09:58:02.585 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 09:58:02.589 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 09:58:02.590 +03:00 [INF] Hosting environment: Production
2025-07-20 09:58:02.591 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 09:58:02.857 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:58:02.888 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:58:02.911 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:58:02.920 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 69.4925ms
2025-07-20 09:58:05.105 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=88Jv1SXOr3imP0hZ1nNm1Q - null null
2025-07-20 09:58:05.111 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 09:58:05.201 +03:00 [INF] Successfully connected to Hub
2025-07-20 09:58:05.204 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 09:58:05.551 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 09:58:05.560 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:58:05.564 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 13.403ms
2025-07-20 09:58:05.576 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 09:58:05.584 +03:00 [INF] CORS policy execution successful.
2025-07-20 09:58:05.587 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:58:05.613 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 09:58:06.092 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 09:58:06.145 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:58:53.549 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 09:58:53.614 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:58:53.616 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-20 09:58:53.621 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=88Jv1SXOr3imP0hZ1nNm1Q - 101 null null 48518.9521ms
2025-07-20 09:58:53.652 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 09:58:53.655 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 47558.6601ms.
2025-07-20 09:58:53.660 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 09:58:53.663 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 48045.3246ms
2025-07-20 09:58:53.666 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 09:58:53.668 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 48093.568ms
2025-07-20 09:58:58.693 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 09:58:58.697 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 09:58:58.699 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 09:58:58.708 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 15.0949ms
2025-07-20 09:59:00.756 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=5Q6k8ckNVwwWfAThtsFmeQ - null null
2025-07-20 09:59:00.758 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:01:36.268 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:01:36.291 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:01:36.293 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:01:36.300 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:01:36.301 +03:00 [INF] Initializing hub connection...
2025-07-20 10:01:37.456 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 10:01:37.531 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 10:01:37.534 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 10:01:37.536 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 10:01:37.537 +03:00 [INF] Hosting environment: Production
2025-07-20 10:01:37.538 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 10:01:38.532 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:01:38.553 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:01:38.567 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:01:38.570 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 41.9906ms
2025-07-20 10:01:40.669 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=tYkgJgnzcEkpCJAoY7_6Og - null null
2025-07-20 10:01:40.678 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:01:40.788 +03:00 [INF] Successfully connected to Hub
2025-07-20 10:01:40.790 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 10:01:49.304 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:01:49.308 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:01:49.309 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 5.5607ms
2025-07-20 10:01:49.313 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:01:49.315 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:01:49.315 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:01:49.324 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:01:49.495 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:01:49.522 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:02:13.649 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-20 10:02:13.654 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=tYkgJgnzcEkpCJAoY7_6Og - 101 null null 32985.9909ms
2025-07-20 10:10:53.384 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:10:53.500 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:10:53.510 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:10:53.619 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:10:53.624 +03:00 [INF] Initializing hub connection...
2025-07-20 10:10:57.789 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 10:10:57.929 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 10:10:57.935 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 10:10:57.943 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 10:10:57.947 +03:00 [INF] Hosting environment: Production
2025-07-20 10:10:57.950 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 10:10:58.429 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:10:58.487 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:10:58.521 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:10:58.528 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 109.224ms
2025-07-20 10:11:00.720 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=EY9qfU5yVPXkSDjoYp789w - null null
2025-07-20 10:11:00.728 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:11:00.786 +03:00 [INF] Successfully connected to Hub
2025-07-20 10:11:00.789 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 10:11:06.435 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:11:06.444 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:11:06.446 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 11.6032ms
2025-07-20 10:11:06.451 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:11:06.455 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:11:06.457 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:11:06.467 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:11:06.649 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:11:06.741 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:11:17.213 +03:00 [ERR] An error occurred during printing: HTML content is invalid or incomplete.
2025-07-20 10:11:17.217 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:11:17.264 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:11:17.268 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 10614.4642ms.
2025-07-20 10:11:17.274 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 10:11:17.279 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 10808.8153ms
2025-07-20 10:11:17.290 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:11:17.292 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 10841.9386ms
2025-07-20 10:12:04.449 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:12:04.491 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:12:04.497 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:12:04.512 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:12:04.514 +03:00 [INF] Initializing hub connection...
2025-07-20 10:12:06.537 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 10:12:06.625 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 10:12:06.628 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 10:12:06.633 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 10:12:06.634 +03:00 [INF] Hosting environment: Production
2025-07-20 10:12:06.635 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 10:12:06.847 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:12:06.886 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:12:06.912 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:12:06.918 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 79.2631ms
2025-07-20 10:12:09.073 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=X8ZtNAAvuEKfZ6AU_dSu1A - null null
2025-07-20 10:12:09.079 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:12:09.153 +03:00 [INF] Successfully connected to Hub
2025-07-20 10:12:09.157 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 10:12:16.741 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:12:16.749 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:12:16.754 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 12.7747ms
2025-07-20 10:12:16.757 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:12:16.763 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:12:16.764 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:12:16.778 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:12:16.974 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:12:17.004 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:12:33.799 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:12:33.800 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:12:33.801 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.8779ms
2025-07-20 10:12:33.803 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:12:33.824 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:12:33.826 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:12:33.828 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:12:33.842 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:12:33.851 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:16:31.815 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:16:31.840 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:16:31.842 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:16:31.851 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:16:31.851 +03:00 [INF] Initializing hub connection...
2025-07-20 10:16:38.846 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:16:38.875 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:16:38.881 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:16:38.892 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:16:38.893 +03:00 [INF] Initializing hub connection...
2025-07-20 10:16:40.099 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 10:16:40.182 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 10:16:40.186 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 10:16:40.189 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 10:16:40.190 +03:00 [INF] Hosting environment: Production
2025-07-20 10:16:40.192 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 10:16:41.141 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:16:41.156 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:16:41.173 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:16:41.176 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 38.2223ms
2025-07-20 10:16:43.281 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=m_hLllprpYucLdadkThjOg - null null
2025-07-20 10:16:43.285 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:16:43.339 +03:00 [INF] Successfully connected to Hub
2025-07-20 10:16:43.340 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 10:16:47.159 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:16:47.165 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:16:47.167 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.4422ms
2025-07-20 10:16:47.172 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:16:47.174 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:16:47.175 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:16:47.186 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:16:47.372 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:16:47.409 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:25:00.560 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:25:00.582 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:25:00.586 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:25:00.595 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:25:00.597 +03:00 [INF] Initializing hub connection...
2025-07-20 10:25:01.837 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 10:25:01.904 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 10:25:01.908 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 10:25:01.913 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 10:25:01.915 +03:00 [INF] Hosting environment: Production
2025-07-20 10:25:01.917 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 10:25:02.826 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:25:02.849 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:25:02.864 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:25:02.867 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 44.1539ms
2025-07-20 10:25:04.956 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=QvPq2EsqvYyZw5Xr18jnpA - null null
2025-07-20 10:25:04.960 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:25:05.001 +03:00 [INF] Successfully connected to Hub
2025-07-20 10:25:05.004 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 10:25:12.806 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:25:12.813 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:25:12.815 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 9.408ms
2025-07-20 10:25:12.819 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:25:12.821 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:25:12.822 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:25:12.832 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:25:13.012 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:25:13.134 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:25:35.019 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:25:35.028 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:25:35.030 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 10.9928ms
2025-07-20 10:25:35.033 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:25:35.047 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:25:35.050 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:25:35.053 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:25:35.071 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:25:35.107 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:25:40.035 +03:00 [ERR] An error occurred during printing: One or more errors occurred. (Can not parse a PDF from an empty byte array.)
2025-07-20 10:25:40.037 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:25:40.080 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:25:40.084 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 5007.8775ms.
2025-07-20 10:25:40.091 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 10:25:40.096 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 5033.8042ms
2025-07-20 10:25:40.098 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:25:40.099 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 5066.3113ms
2025-07-20 10:25:55.658 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:25:55.661 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:25:55.662 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 4.0628ms
2025-07-20 10:25:55.664 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:25:55.671 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:25:55.672 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:25:55.673 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:25:55.685 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:25:55.700 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:26:01.394 +03:00 [ERR] An error occurred during printing: One or more errors occurred. (Can not parse a PDF from an empty byte array.)
2025-07-20 10:26:01.396 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:26:01.459 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:26:01.464 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 5775.2639ms.
2025-07-20 10:26:01.494 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 10:26:01.515 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 5838.6937ms
2025-07-20 10:26:01.518 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:26:01.521 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 5856.777ms
2025-07-20 10:28:54.234 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:28:54.260 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:28:54.265 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:28:54.274 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:28:54.276 +03:00 [INF] Initializing hub connection...
2025-07-20 10:28:55.143 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 10:28:55.190 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 10:28:55.193 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 10:28:55.195 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 10:28:55.197 +03:00 [INF] Hosting environment: Production
2025-07-20 10:28:55.199 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 10:28:56.505 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:28:56.523 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:28:56.537 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:28:56.541 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 38.2335ms
2025-07-20 10:28:58.628 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=-qH2s2XVJJLpc-wzJVm-VA - null null
2025-07-20 10:28:58.636 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:28:58.673 +03:00 [INF] Successfully connected to Hub
2025-07-20 10:28:58.676 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 10:29:06.563 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:29:06.578 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:29:06.581 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 18.0143ms
2025-07-20 10:29:06.587 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:29:06.590 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:29:06.591 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:29:06.602 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:29:06.810 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:29:06.893 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:29:25.017 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 10:29:25.018 +03:00 [INF] COM ports populated.
2025-07-20 10:29:25.055 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 10:29:25.056 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 10:29:25.057 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 10:29:25.059 +03:00 [INF] TabPage returned successfully.
2025-07-20 10:29:25.168 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 10:29:25.188 +03:00 [INF] Total tabs in control: 5
2025-07-20 10:29:25.194 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 10:29:25.196 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 10:29:25.197 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 10:29:25.198 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 10:29:25.199 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 10:29:36.633 +03:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null null
2025-07-20 10:29:36.639 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:29:36.640 +03:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 204 null null 7.788ms
2025-07-20 10:29:36.646 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:29:36.651 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:29:36.652 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:29:36.656 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:29:36.658 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 11.6282ms
2025-07-20 10:29:37.143 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=2fZ6ndVvw1d1jrnT3mHyng - null null
2025-07-20 10:29:37.150 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:29:37.152 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:30:55.339 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:30:55.341 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:30:55.342 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 3.1328ms
2025-07-20 10:30:55.344 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:30:55.346 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:30:55.347 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:30:55.348 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:30:55.360 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:39:19.240 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:39:19.264 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:39:19.267 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:39:19.276 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:39:19.278 +03:00 [INF] Initializing hub connection...
2025-07-20 10:39:20.142 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 10:39:20.181 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 10:39:20.186 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 10:39:20.189 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 10:39:20.191 +03:00 [INF] Hosting environment: Production
2025-07-20 10:39:20.192 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 10:39:21.509 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:39:21.528 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:39:21.541 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:39:21.543 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 36.4231ms
2025-07-20 10:39:23.644 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=tnqQwxwkTR94s0j8eJINcw - null null
2025-07-20 10:39:23.648 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:39:23.694 +03:00 [INF] Successfully connected to Hub
2025-07-20 10:39:23.695 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 10:39:29.827 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:39:29.833 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:39:29.836 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 8.4306ms
2025-07-20 10:39:29.839 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:39:29.842 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:39:29.843 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:39:29.856 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:39:30.041 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:39:30.097 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:40:57.226 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:40:57.300 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:40:57.313 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:40:57.333 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:40:57.339 +03:00 [INF] Initializing hub connection...
2025-07-20 10:40:59.453 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 10:40:59.557 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 10:40:59.562 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 10:40:59.567 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 10:40:59.569 +03:00 [INF] Hosting environment: Production
2025-07-20 10:40:59.572 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 10:40:59.777 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:40:59.820 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:40:59.850 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:40:59.856 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 86.0236ms
2025-07-20 10:41:02.031 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=sICtKxVrMNg7W6bTKWVYnA - null null
2025-07-20 10:41:02.043 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:41:02.139 +03:00 [INF] Successfully connected to Hub
2025-07-20 10:41:02.142 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 10:41:10.214 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:41:10.228 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:41:10.233 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 19.3762ms
2025-07-20 10:41:10.243 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:41:10.248 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:41:10.249 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:41:10.270 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:41:10.683 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:41:10.772 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:41:45.441 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:41:45.479 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:41:45.483 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:41:45.495 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:41:45.496 +03:00 [INF] Initializing hub connection...
2025-07-20 10:41:47.477 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 10:41:47.570 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 10:41:47.577 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 10:41:47.581 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 10:41:47.584 +03:00 [INF] Hosting environment: Production
2025-07-20 10:41:47.587 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 10:41:47.797 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:41:47.834 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:41:47.858 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:41:47.865 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 75.71ms
2025-07-20 10:41:50.028 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=bDyd_N9ZCRn-P-3SaPoRZw - null null
2025-07-20 10:41:50.035 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:41:50.107 +03:00 [INF] Successfully connected to Hub
2025-07-20 10:41:50.110 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 10:41:53.248 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:41:53.259 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:41:53.262 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 13.9514ms
2025-07-20 10:41:53.268 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:41:53.277 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:41:53.279 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:41:53.293 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:41:53.512 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:41:53.551 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:42:21.335 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:42:21.360 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:42:21.364 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:42:21.374 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:42:21.375 +03:00 [INF] Initializing hub connection...
2025-07-20 10:42:22.558 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 10:42:22.616 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 10:42:22.620 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 10:42:22.624 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 10:42:22.626 +03:00 [INF] Hosting environment: Production
2025-07-20 10:42:22.627 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 10:42:23.629 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:42:23.665 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:42:23.687 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:42:23.695 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 69.908ms
2025-07-20 10:42:25.834 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=9jWMn9vSI6BfsW-TpuVfGA - null null
2025-07-20 10:42:25.841 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:42:25.889 +03:00 [INF] Successfully connected to Hub
2025-07-20 10:42:25.892 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 10:42:36.334 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:42:36.343 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:42:36.345 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 10.2523ms
2025-07-20 10:42:36.349 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:42:36.353 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:42:36.354 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:42:36.365 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:42:36.572 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:42:36.598 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:43:28.719 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 10:43:28.804 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 10:43:28.822 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 10:43:28.856 +03:00 [INF] Capture image mode set to: false
2025-07-20 10:43:28.863 +03:00 [INF] Initializing hub connection...
2025-07-20 10:43:31.459 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 10:43:31.552 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 10:43:31.558 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 10:43:31.563 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 10:43:31.565 +03:00 [INF] Hosting environment: Production
2025-07-20 10:43:31.567 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 10:43:31.866 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 10:43:31.908 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 10:43:31.937 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 10:43:31.943 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 84.394ms
2025-07-20 10:43:34.118 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=url1aR3AjIiuTftPMcMhaQ - null null
2025-07-20 10:43:34.126 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 10:43:34.213 +03:00 [INF] Successfully connected to Hub
2025-07-20 10:43:34.272 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 10:43:47.725 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:43:47.734 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:43:47.738 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 13.1958ms
2025-07-20 10:43:47.747 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:43:47.751 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:43:47.753 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:43:47.771 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:43:48.059 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:43:48.089 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:44:01.791 +03:00 [ERR] An error occurred during printing: The process cannot access the file 'C:\Users\<USER>\AppData\Local\Temp\b56eef54-9531-4aa0-a4e3-2e1801211c1c.pdf' because it is being used by another process.
2025-07-20 10:44:01.794 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:44:01.860 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 10:44:01.866 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 13802.0712ms.
2025-07-20 10:44:01.872 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 10:44:01.877 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 14101.1355ms
2025-07-20 10:44:01.880 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:44:01.882 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 14135.397ms
2025-07-20 10:44:09.455 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 10:44:09.458 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:44:09.459 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 4.705ms
2025-07-20 10:44:09.462 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 10:44:09.468 +03:00 [INF] CORS policy execution successful.
2025-07-20 10:44:09.470 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 10:44:09.472 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 10:44:09.484 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 10:44:09.534 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:00:04.517 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 11:00:04.585 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 11:00:04.593 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 11:00:08.536 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 11:00:08.642 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 11:00:08.647 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 11:00:08.653 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:00:08.655 +03:00 [INF] Hosting environment: Production
2025-07-20 11:00:08.657 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 11:00:58.806 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 11:00:58.840 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:00:58.846 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 45.4345ms
2025-07-20 11:00:58.862 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 11:00:58.866 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:00:58.870 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:00:58.891 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 11:00:59.203 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 11:00:59.230 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:28:15.049 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 11:28:15.132 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 11:28:15.142 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 11:28:17.683 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 11:28:17.816 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 11:28:17.823 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 11:28:17.832 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:28:17.834 +03:00 [INF] Hosting environment: Production
2025-07-20 11:28:17.836 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 11:28:32.268 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 11:28:32.303 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:28:32.309 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 49.5522ms
2025-07-20 11:28:32.319 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 11:28:32.327 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:28:32.333 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:28:32.358 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 11:28:32.784 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 11:28:32.878 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:34:49.388 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 11:34:49.442 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 11:34:49.449 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 11:34:51.601 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 11:34:51.681 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 11:34:51.684 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 11:34:51.689 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:34:51.690 +03:00 [INF] Hosting environment: Production
2025-07-20 11:34:51.692 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 11:35:02.913 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 11:35:02.914 +03:00 [INF] COM ports populated.
2025-07-20 11:35:04.052 +03:00 [INF] No barcode scanner detected.
2025-07-20 11:35:04.056 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-20 11:35:04.057 +03:00 [INF] TabPage returned successfully.
2025-07-20 11:35:04.209 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 11:35:04.225 +03:00 [INF] Total tabs in control: 5
2025-07-20 11:35:04.229 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 11:35:04.230 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 11:35:04.231 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 11:35:04.238 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 11:35:04.238 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 11:35:07.341 +03:00 [INF] Port URL changed from 'https://127.0.0.1:7000' to 'https://127.0.0.1:7000/' - restart required
2025-07-20 11:35:35.576 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 11:35:35.597 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 11:35:35.599 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 11:35:35.606 +03:00 [INF] Capture image mode set to: false
2025-07-20 11:35:35.607 +03:00 [INF] Initializing hub connection...
2025-07-20 11:35:36.657 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 11:35:36.694 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 11:35:36.695 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 11:35:36.697 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:35:36.697 +03:00 [INF] Hosting environment: Production
2025-07-20 11:35:36.698 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 11:35:37.833 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 11:35:37.849 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 11:35:37.861 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 11:35:37.863 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 32.9119ms
2025-07-20 11:35:40.017 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=onTvEXh7ti7xe2QL91cPjg - null null
2025-07-20 11:35:40.025 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 11:35:40.076 +03:00 [INF] Successfully connected to Hub
2025-07-20 11:35:40.076 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 11:37:02.067 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 11:37:02.072 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:37:02.073 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 6.0713ms
2025-07-20 11:37:02.077 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 11:37:02.080 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:37:02.081 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:37:02.090 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 11:37:02.259 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 11:37:02.283 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:43:47.048 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 11:43:47.111 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 11:43:47.117 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 11:43:47.133 +03:00 [INF] Capture image mode set to: false
2025-07-20 11:43:47.134 +03:00 [INF] Initializing hub connection...
2025-07-20 11:43:49.599 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 11:43:49.700 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 11:43:49.704 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 11:43:49.708 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:43:49.710 +03:00 [INF] Hosting environment: Production
2025-07-20 11:43:49.714 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 11:43:50.085 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 11:43:50.113 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 11:43:50.136 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 11:43:50.140 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 60.769ms
2025-07-20 11:43:52.303 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=0P9xrTnEOt7zISrMMctuUw - null null
2025-07-20 11:43:52.310 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 11:43:52.380 +03:00 [INF] Successfully connected to Hub
2025-07-20 11:43:52.382 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 11:43:56.706 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 11:43:56.712 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:43:56.713 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.4145ms
2025-07-20 11:43:56.717 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 11:43:56.719 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:43:56.719 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:43:56.729 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 11:43:56.897 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 11:43:56.926 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:45:05.932 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 11:45:05.955 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 11:45:05.958 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 11:45:05.969 +03:00 [INF] Capture image mode set to: false
2025-07-20 11:45:05.970 +03:00 [INF] Initializing hub connection...
2025-07-20 11:45:07.111 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 11:45:07.146 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 11:45:07.148 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 11:45:07.149 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:45:07.150 +03:00 [INF] Hosting environment: Production
2025-07-20 11:45:07.150 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 11:45:08.176 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 11:45:08.193 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 11:45:08.224 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 11:45:08.227 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 53.8394ms
2025-07-20 11:45:10.327 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=WjDw9pS5m-EFnhbZgbG6ug - null null
2025-07-20 11:45:10.331 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 11:45:10.403 +03:00 [INF] Successfully connected to Hub
2025-07-20 11:45:10.405 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 11:45:11.663 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 11:45:11.667 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:45:11.669 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 5.7462ms
2025-07-20 11:45:11.672 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 11:45:11.674 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:45:11.674 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:45:11.684 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 11:45:11.855 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 11:45:11.887 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:45:34.335 +03:00 [ERR] An error occurred during printing: The process cannot access the file 'C:\Users\<USER>\AppData\Local\Temp\4088326a-4012-42d4-b785-42d2c0f3bfd1.pdf' because it is being used by another process.
2025-07-20 11:45:34.339 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:45:34.403 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:45:34.407 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 22546.7238ms.
2025-07-20 11:45:34.412 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 11:45:34.416 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 22729.1896ms
2025-07-20 11:45:34.417 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:45:34.418 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 22745.8366ms
2025-07-20 11:46:40.977 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 11:46:40.978 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:46:40.979 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.0261ms
2025-07-20 11:46:40.980 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 11:46:40.982 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:46:40.983 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:46:40.983 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 11:46:40.995 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 11:46:41.070 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:48:20.853 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 11:48:20.963 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 11:48:20.978 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 11:48:21.006 +03:00 [INF] Capture image mode set to: false
2025-07-20 11:48:21.012 +03:00 [INF] Initializing hub connection...
2025-07-20 11:48:23.678 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 11:48:23.775 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 11:48:23.779 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 11:48:23.784 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:48:23.786 +03:00 [INF] Hosting environment: Production
2025-07-20 11:48:23.787 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 11:48:24.059 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 11:48:24.104 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 11:48:24.128 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 11:48:24.131 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 80.2124ms
2025-07-20 11:48:26.276 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=y8J3E6MNiBESX5Tr8tjHwA - null null
2025-07-20 11:48:26.282 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 11:48:26.371 +03:00 [INF] Successfully connected to Hub
2025-07-20 11:48:26.375 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 11:48:32.311 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 11:48:32.317 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:48:32.319 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.5094ms
2025-07-20 11:48:32.323 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 11:48:32.324 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:48:32.325 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:48:32.335 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 11:48:32.509 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 11:48:32.617 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:49:59.731 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-20 11:54:06.918 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 11:54:06.942 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 11:54:06.945 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 11:54:06.956 +03:00 [INF] Capture image mode set to: false
2025-07-20 11:54:06.957 +03:00 [INF] Initializing hub connection...
2025-07-20 11:54:07.854 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 11:54:07.893 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 11:54:07.894 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 11:54:07.895 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:54:07.897 +03:00 [INF] Hosting environment: Production
2025-07-20 11:54:07.897 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 11:54:09.185 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 11:54:09.204 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 11:54:09.219 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 11:54:09.223 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 40.9343ms
2025-07-20 11:54:11.321 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=eZE6huZARgVq7DoIBGxJFw - null null
2025-07-20 11:54:11.329 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 11:54:11.412 +03:00 [INF] Successfully connected to Hub
2025-07-20 11:54:11.413 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 11:54:33.368 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 11:54:33.373 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:54:33.374 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 6.543ms
2025-07-20 11:54:33.378 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 11:54:33.380 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:54:33.381 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:54:33.392 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 11:54:33.570 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 11:54:33.604 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:54:50.211 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:54:50.260 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:54:50.265 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 16690.3114ms.
2025-07-20 11:54:50.270 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 11:54:50.276 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 16880.5541ms
2025-07-20 11:54:50.277 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:54:50.277 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 16899.5509ms
2025-07-20 11:55:56.248 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 11:55:56.272 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 11:55:56.275 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 11:55:56.284 +03:00 [INF] Capture image mode set to: false
2025-07-20 11:55:56.285 +03:00 [INF] Initializing hub connection...
2025-07-20 11:55:57.487 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 11:55:57.525 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 11:55:57.527 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 11:55:57.529 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 11:55:57.530 +03:00 [INF] Hosting environment: Production
2025-07-20 11:55:57.531 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 11:55:58.519 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 11:55:58.539 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 11:55:58.552 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 11:55:58.553 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 37.9869ms
2025-07-20 11:56:00.664 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=PvUvpEMGtcBQLyPMVlrpuA - null null
2025-07-20 11:56:00.671 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 11:56:00.726 +03:00 [INF] Successfully connected to Hub
2025-07-20 11:56:00.728 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 11:56:39.958 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 11:56:39.962 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:56:39.964 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 6.0137ms
2025-07-20 11:56:39.968 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 11:56:39.970 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:56:39.970 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:56:39.980 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 11:56:40.157 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 11:56:40.266 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:56:46.148 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:56:46.202 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 11:56:46.205 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 6043.4545ms.
2025-07-20 11:56:46.211 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 11:56:46.217 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 6230.1249ms
2025-07-20 11:56:46.218 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:56:46.219 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 6250.5122ms
2025-07-20 11:57:08.793 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 11:57:08.796 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:57:08.798 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 5.0158ms
2025-07-20 11:57:08.800 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 11:57:08.803 +03:00 [INF] CORS policy execution successful.
2025-07-20 11:57:08.804 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 11:57:08.805 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 11:57:08.817 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 11:57:08.827 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:03:16.700 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 12:03:16.805 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 12:03:16.816 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 12:03:16.849 +03:00 [INF] Capture image mode set to: false
2025-07-20 12:03:16.851 +03:00 [INF] Initializing hub connection...
2025-07-20 12:03:19.648 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 12:03:19.769 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 12:03:19.774 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 12:03:19.780 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:03:19.783 +03:00 [INF] Hosting environment: Production
2025-07-20 12:03:19.785 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 12:03:19.967 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 12:03:20.027 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 12:03:20.062 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 12:03:20.068 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 112.3427ms
2025-07-20 12:03:22.305 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=1vTN3zcIvKBIdPpErv4h0Q - null null
2025-07-20 12:03:22.313 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 12:03:22.433 +03:00 [INF] Successfully connected to Hub
2025-07-20 12:03:22.435 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 12:03:37.873 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 12:03:37.881 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:03:37.883 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 10.3693ms
2025-07-20 12:03:37.890 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 12:03:37.894 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:03:37.896 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:03:37.916 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 12:03:38.288 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 12:03:38.342 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:04:42.171 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 12:04:42.195 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 12:04:42.200 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 12:04:42.208 +03:00 [INF] Capture image mode set to: false
2025-07-20 12:04:42.209 +03:00 [INF] Initializing hub connection...
2025-07-20 12:04:43.608 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 12:04:43.666 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 12:04:43.668 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 12:04:43.670 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:04:43.671 +03:00 [INF] Hosting environment: Production
2025-07-20 12:04:43.672 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 12:04:44.483 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 12:04:44.514 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 12:04:44.533 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 12:04:44.539 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 60.2509ms
2025-07-20 12:04:46.653 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=NBorWugPWu7rXox59UBbOw - null null
2025-07-20 12:04:46.656 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 12:04:46.706 +03:00 [INF] Successfully connected to Hub
2025-07-20 12:04:46.708 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 12:04:52.920 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 12:04:52.925 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:04:52.928 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.8095ms
2025-07-20 12:04:52.933 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 12:04:52.936 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:04:52.937 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:04:52.953 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 12:04:53.203 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 12:04:53.245 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:13:21.767 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 12:13:21.789 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 12:13:21.794 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 12:13:21.803 +03:00 [INF] Capture image mode set to: false
2025-07-20 12:13:21.803 +03:00 [INF] Initializing hub connection...
2025-07-20 12:13:22.662 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 12:13:22.704 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 12:13:22.706 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 12:13:22.708 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:13:22.708 +03:00 [INF] Hosting environment: Production
2025-07-20 12:13:22.709 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 12:13:24.019 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 12:13:24.039 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 12:13:24.052 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 12:13:24.055 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 38.6298ms
2025-07-20 12:13:26.158 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=PpjzFB5TDkHg6jbdfnEwIg - null null
2025-07-20 12:13:26.168 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 12:13:26.259 +03:00 [INF] Successfully connected to Hub
2025-07-20 12:13:26.260 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 12:13:36.572 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 12:13:36.578 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:13:36.579 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.0661ms
2025-07-20 12:13:36.584 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 12:13:36.586 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:13:36.586 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:13:36.598 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 12:13:36.801 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 12:13:36.897 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:13:57.844 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 12:13:57.849 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:13:57.853 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-20 12:13:57.855 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=PpjzFB5TDkHg6jbdfnEwIg - 101 null null 31698.1782ms
2025-07-20 12:13:57.902 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:13:57.906 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 21099.5282ms.
2025-07-20 12:13:57.912 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 12:13:57.916 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 21312.0082ms
2025-07-20 12:13:57.917 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:13:57.917 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 21333.4624ms
2025-07-20 12:14:01.932 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 12:14:03.854 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 12:14:03.858 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 12:14:03.877 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 1945.8049ms
2025-07-20 12:14:05.924 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=qNukjITI-eBGtDYNwhMKnA - null null
2025-07-20 12:14:05.930 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 12:14:23.535 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 12:14:23.537 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:14:23.539 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 3.5238ms
2025-07-20 12:14:23.540 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 12:14:23.542 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:14:23.543 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:14:23.544 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 12:14:23.554 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 12:14:23.587 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:14:55.650 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-20 12:14:55.722 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=qNukjITI-eBGtDYNwhMKnA - 101 null null 49798.1485ms
2025-07-20 12:16:53.024 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 12:16:53.128 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 12:16:53.136 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 12:16:53.163 +03:00 [INF] Capture image mode set to: false
2025-07-20 12:16:53.165 +03:00 [INF] Initializing hub connection...
2025-07-20 12:16:56.661 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 12:16:56.782 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 12:16:56.789 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 12:16:56.798 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:16:56.801 +03:00 [INF] Hosting environment: Production
2025-07-20 12:16:56.803 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 12:16:57.273 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 12:16:57.334 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 12:16:57.371 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 12:16:57.378 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 116.2349ms
2025-07-20 12:16:59.555 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=dPVl__IJAowXB6T5MdOryg - null null
2025-07-20 12:16:59.559 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 12:16:59.657 +03:00 [INF] Successfully connected to Hub
2025-07-20 12:16:59.660 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 12:17:07.712 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 12:17:07.718 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:17:07.719 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.1793ms
2025-07-20 12:17:07.724 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 12:17:07.727 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:17:07.727 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:17:07.738 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 12:17:07.920 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 12:17:07.993 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:20:37.201 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 12:20:37.223 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 12:20:37.225 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 12:20:37.233 +03:00 [INF] Capture image mode set to: false
2025-07-20 12:20:37.234 +03:00 [INF] Initializing hub connection...
2025-07-20 12:20:38.141 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 12:20:38.189 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 12:20:38.190 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 12:20:38.192 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:20:38.192 +03:00 [INF] Hosting environment: Production
2025-07-20 12:20:38.193 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 12:20:39.498 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 12:20:39.517 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 12:20:39.529 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 12:20:39.531 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 36.3282ms
2025-07-20 12:20:41.653 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=D3Xhc0SzRMQru5EH450j1Q - null null
2025-07-20 12:20:41.657 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 12:20:41.699 +03:00 [INF] Successfully connected to Hub
2025-07-20 12:20:41.700 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 12:20:42.740 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 12:20:42.744 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:20:42.745 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 5.6915ms
2025-07-20 12:20:42.750 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 12:20:42.751 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:20:42.752 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:20:42.763 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 12:20:42.939 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 12:20:42.968 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:21:11.364 +03:00 [ERR] An error occurred during printing: Failed to generate PDF: Can not parse a PDF from an empty byte array.
2025-07-20 12:21:11.368 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:21:11.431 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:21:11.434 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 28489.8437ms.
2025-07-20 12:21:11.438 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 12:21:11.442 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 28676.1751ms
2025-07-20 12:21:11.443 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:21:11.444 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 28694.4435ms
2025-07-20 12:23:31.376 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 12:23:31.378 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:23:31.378 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.342ms
2025-07-20 12:23:31.380 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 12:23:31.382 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:23:31.382 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:23:31.383 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 12:23:31.392 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 12:23:31.402 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:32:27.870 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 12:32:27.949 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 12:32:27.958 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 12:32:27.981 +03:00 [INF] Capture image mode set to: false
2025-07-20 12:32:27.983 +03:00 [INF] Initializing hub connection...
2025-07-20 12:32:30.460 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 12:32:30.561 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 12:32:30.566 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 12:32:30.571 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:32:30.572 +03:00 [INF] Hosting environment: Production
2025-07-20 12:32:30.573 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 12:32:31.040 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 12:32:31.079 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 12:32:31.103 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 12:32:31.108 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 75.082ms
2025-07-20 12:32:33.249 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=zvVQ-PNmJMT8sgSJ7wVDAw - null null
2025-07-20 12:32:33.256 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 12:32:33.338 +03:00 [INF] Successfully connected to Hub
2025-07-20 12:32:33.341 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 12:32:46.073 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 12:32:46.077 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:32:46.079 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 5.7107ms
2025-07-20 12:32:46.083 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 12:32:46.086 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:32:46.087 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:32:46.097 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 12:32:46.269 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 12:32:46.371 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:32:52.353 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:32:52.400 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:32:52.403 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 6128.9909ms.
2025-07-20 12:32:52.408 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-20 12:32:52.412 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 6311.9487ms
2025-07-20 12:32:52.414 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:32:52.415 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 200 null text/plain; charset=utf-8 6331.5887ms
2025-07-20 12:33:22.194 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 12:33:22.196 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:33:22.196 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.7229ms
2025-07-20 12:33:22.198 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 12:33:22.201 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:33:22.202 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:33:22.202 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 12:33:22.212 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 12:33:22.256 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:33:22.990 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:33:23.102 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:33:23.103 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 889.7626ms.
2025-07-20 12:33:23.105 +03:00 [INF] Executing OkObjectResult, writing value of type 'System.String'.
2025-07-20 12:33:23.106 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 901.9649ms
2025-07-20 12:33:23.107 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:33:23.108 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 200 null text/plain; charset=utf-8 909.6677ms
2025-07-20 12:34:26.977 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 12:34:26.979 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:34:26.980 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 3.2558ms
2025-07-20 12:34:26.983 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 12:34:27.058 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:34:27.059 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:34:27.060 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 12:34:27.070 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 12:34:27.078 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 12:54:34.458 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 12:54:34.500 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 12:54:34.503 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 12:54:34.510 +03:00 [INF] Capture image mode set to: false
2025-07-20 12:54:34.512 +03:00 [INF] Initializing hub connection...
2025-07-20 12:54:36.457 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 12:54:36.494 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 12:54:36.496 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 12:54:36.498 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:54:36.498 +03:00 [INF] Hosting environment: Production
2025-07-20 12:54:36.498 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 12:54:36.753 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 12:54:36.771 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 12:54:36.783 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 12:54:36.785 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 34.2281ms
2025-07-20 12:55:10.053 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 12:55:10.132 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 12:55:10.145 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 12:55:10.171 +03:00 [INF] Capture image mode set to: false
2025-07-20 12:55:10.174 +03:00 [INF] Initializing hub connection...
2025-07-20 12:55:12.835 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 12:55:12.916 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 12:55:12.918 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 12:55:12.922 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 12:55:12.923 +03:00 [INF] Hosting environment: Production
2025-07-20 12:55:12.924 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 12:55:13.224 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 12:55:13.257 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 12:55:13.281 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 12:55:13.286 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 68.1129ms
2025-07-20 12:55:15.412 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=bOJUcclxF_RMuOf_vS2_dw - null null
2025-07-20 12:55:15.416 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 12:55:15.501 +03:00 [INF] Successfully connected to Hub
2025-07-20 12:55:15.503 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 12:55:23.980 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 12:55:23.981 +03:00 [INF] COM ports populated.
2025-07-20 12:55:24.006 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 12:55:24.007 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 12:55:24.007 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 12:55:24.008 +03:00 [INF] TabPage returned successfully.
2025-07-20 12:55:24.108 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 12:55:24.124 +03:00 [INF] Total tabs in control: 5
2025-07-20 12:55:24.128 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 12:55:24.129 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 12:55:24.129 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 12:55:24.130 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 12:55:24.130 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 12:57:33.914 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 12:57:33.919 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:57:33.920 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 6.5775ms
2025-07-20 12:57:33.925 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 12:57:33.926 +03:00 [INF] CORS policy execution successful.
2025-07-20 12:57:33.928 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 12:57:33.939 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 12:57:34.117 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:00:20.754 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:00:20.778 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:00:20.780 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:00:20.788 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:00:20.789 +03:00 [INF] Initializing hub connection...
2025-07-20 13:00:21.990 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:00:22.025 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:00:22.027 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:00:22.029 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:00:22.029 +03:00 [INF] Hosting environment: Production
2025-07-20 13:00:22.030 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:00:23.032 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:00:23.062 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:00:23.075 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:00:23.077 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 48.2261ms
2025-07-20 13:00:25.181 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=8RjfXHss-mNDlZcOUeei_A - null null
2025-07-20 13:00:25.189 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:00:25.240 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:00:25.242 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:00:32.035 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:00:32.040 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:00:32.042 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.0689ms
2025-07-20 13:00:32.046 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:00:32.048 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:00:32.048 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:00:32.058 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:00:32.229 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:00:32.251 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:00:53.245 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:00:53.247 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:00:53.247 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.3019ms
2025-07-20 13:00:53.249 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:00:53.251 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:00:53.251 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:00:53.252 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:00:53.263 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:00:53.303 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:00:57.095 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 13:00:57.098 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:00:57.149 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:00:57.152 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 3886.4781ms.
2025-07-20 13:00:57.160 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 13:00:57.164 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 3909.211ms
2025-07-20 13:00:57.165 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:00:57.166 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 3917.6603ms
2025-07-20 13:03:01.163 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:03:01.164 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:03:01.165 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 1.603ms
2025-07-20 13:03:01.167 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:03:01.169 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:03:01.170 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:03:01.171 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:03:01.179 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:03:01.192 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:06:16.000 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:06:16.023 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:06:16.025 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:06:16.034 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:06:16.035 +03:00 [INF] Initializing hub connection...
2025-07-20 13:06:17.242 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:06:17.278 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:06:17.279 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:06:17.280 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:06:17.281 +03:00 [INF] Hosting environment: Production
2025-07-20 13:06:17.282 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:06:18.262 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:06:18.281 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:06:18.294 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:06:18.297 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 36.9664ms
2025-07-20 13:06:20.455 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=tiI57WW2ha1WWeCn20Ee8A - null null
2025-07-20 13:06:20.462 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:06:20.514 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:06:20.516 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:06:26.097 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:06:26.102 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:06:26.104 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 6.7187ms
2025-07-20 13:06:26.108 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:06:26.111 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:06:26.112 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:06:26.122 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:06:26.301 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:08:13.528 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:08:13.531 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-20 13:08:13.545 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=tiI57WW2ha1WWeCn20Ee8A - 101 null null 113090.5976ms
2025-07-20 13:08:15.579 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:08:15.584 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:08:15.585 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:08:15.642 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 63.3802ms
2025-07-20 13:08:17.683 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=m8JwxA_BtCu-Qo2Ba4fOyw - null null
2025-07-20 13:08:17.687 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:09:45.605 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:09:45.628 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:09:45.632 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:09:45.640 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:09:45.641 +03:00 [INF] Initializing hub connection...
2025-07-20 13:09:46.799 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:09:46.836 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:09:46.838 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:09:46.840 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:09:46.841 +03:00 [INF] Hosting environment: Production
2025-07-20 13:09:46.841 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:09:47.906 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:09:47.923 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:09:47.935 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:09:47.937 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 34.6117ms
2025-07-20 13:09:50.046 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=KScbOlymYr82ggasbXjYCQ - null null
2025-07-20 13:09:50.055 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:09:50.109 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:09:50.111 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:09:57.952 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:09:57.958 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:09:57.959 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.3247ms
2025-07-20 13:09:57.963 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:09:57.965 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:09:57.965 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:09:57.976 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:09:58.145 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:09:58.182 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:10:10.093 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:10:10.095 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:10:10.096 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.7684ms
2025-07-20 13:10:10.097 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:10:10.099 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:10:10.099 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:10:10.100 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:10:10.110 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:10:10.116 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:10:13.366 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 13:10:13.368 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:10:13.418 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:10:13.422 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 3307.8342ms.
2025-07-20 13:10:13.430 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 13:10:13.437 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 3333.2945ms
2025-07-20 13:10:13.439 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:10:13.440 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 3342.9858ms
2025-07-20 13:10:26.135 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:10:26.136 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:10:26.136 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 1.6249ms
2025-07-20 13:10:26.140 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:10:26.143 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:10:26.143 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:10:26.144 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:10:26.152 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:10:26.160 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:11:59.063 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 13:11:59.107 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:11:59.149 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:11:59.179 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:11:59.182 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:11:59.179 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:11:59.107 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:11:59.179 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:11:59.194 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-20 13:11:59.196 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:11:59.197 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 499 null null 90.4322ms
2025-07-20 13:11:59.199 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:11:59.202 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:11:59.205 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:11:59.206 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=KScbOlymYr82ggasbXjYCQ - 101 null null 129160.2762ms
2025-07-20 13:11:59.219 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:11:59.210 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:11:59.213 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:11:59.214 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 499 null null 108.7708ms
2025-07-20 13:11:59.215 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 36.4457ms
2025-07-20 13:11:59.207 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 28.5153ms
2025-07-20 13:11:59.221 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:11:59.223 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:11:59.224 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:11:59.230 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:11:59.230 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:11:59.240 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:11:59.241 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:11:59.243 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:11:59.245 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:11:59.257 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:11:59.257 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:11:59.276 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:11:59.276 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 93123.6161ms.
2025-07-20 13:11:59.284 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 13:11:59.286 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 93140.5625ms
2025-07-20 13:11:59.286 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:11:59.287 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 93146.855ms
2025-07-20 13:11:59.307 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:11:59.319 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:12:14.901 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:12:14.906 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:12:14.907 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:12:14.930 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 29.3169ms
2025-07-20 13:12:16.767 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 13:12:16.770 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:12:16.821 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:12:16.822 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 17577.0112ms.
2025-07-20 13:12:16.826 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 13:12:16.828 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 17595.753ms
2025-07-20 13:12:16.829 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:12:16.831 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 17652.4143ms
2025-07-20 13:12:16.966 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=OyuMMgSWlnFplJR-ZM0gJg - null null
2025-07-20 13:12:16.971 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:12:20.764 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 13:12:20.766 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:12:20.823 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:12:20.824 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 21545.6036ms.
2025-07-20 13:12:20.825 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 13:12:20.826 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 21578.5024ms
2025-07-20 13:12:20.827 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:12:20.829 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 21609.4782ms
2025-07-20 13:15:01.500 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:15:01.533 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:15:01.540 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:15:01.552 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:15:01.553 +03:00 [INF] Initializing hub connection...
2025-07-20 13:15:03.080 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:15:03.152 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:15:03.156 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:15:03.159 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:15:03.160 +03:00 [INF] Hosting environment: Production
2025-07-20 13:15:03.161 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:15:03.894 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:15:03.928 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:15:03.951 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:15:03.959 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 70.5293ms
2025-07-20 13:15:06.106 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=hlbdZxvTQWjvdsLlRXQ-PQ - null null
2025-07-20 13:15:06.112 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:15:06.162 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:15:06.164 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:15:59.558 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:15:59.564 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:15:59.565 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.3178ms
2025-07-20 13:15:59.570 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:15:59.571 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:15:59.572 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:15:59.584 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:15:59.753 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:15:59.782 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:16:04.023 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 13:16:04.024 +03:00 [INF] COM ports populated.
2025-07-20 13:16:04.050 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 13:16:04.051 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 13:16:04.051 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 13:16:04.052 +03:00 [INF] TabPage returned successfully.
2025-07-20 13:16:04.150 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 13:16:04.166 +03:00 [INF] Total tabs in control: 5
2025-07-20 13:16:04.170 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 13:16:04.170 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 13:16:04.171 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 13:16:04.171 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 13:16:04.172 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 13:17:50.650 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:17:50.676 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:17:50.681 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:17:50.690 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:17:50.690 +03:00 [INF] Initializing hub connection...
2025-07-20 13:17:51.653 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:17:51.690 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:17:51.692 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:17:51.693 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:17:51.701 +03:00 [INF] Hosting environment: Production
2025-07-20 13:17:51.702 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:17:52.899 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:17:52.918 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:17:52.933 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:17:52.935 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 39.263ms
2025-07-20 13:17:55.059 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=3Nphtk0PoHueYvdEPNIppg - null null
2025-07-20 13:17:55.066 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:17:55.120 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:17:55.123 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:20:47.858 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:20:47.933 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:20:47.942 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:20:47.961 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:20:47.964 +03:00 [INF] Initializing hub connection...
2025-07-20 13:20:50.140 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:20:50.225 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:20:50.229 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:20:50.235 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:20:50.236 +03:00 [INF] Hosting environment: Production
2025-07-20 13:20:50.238 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:20:50.452 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:20:50.492 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:20:50.519 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:20:50.525 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 79.1855ms
2025-07-20 13:20:52.708 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=QPSH-OgAjEQEUK3PvrXe7w - null null
2025-07-20 13:20:52.716 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:20:52.784 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:20:52.787 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:23:04.093 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:23:04.116 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:23:04.118 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:23:04.126 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:23:04.127 +03:00 [INF] Initializing hub connection...
2025-07-20 13:23:05.097 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:23:05.143 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:23:05.144 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:23:05.146 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:23:05.148 +03:00 [INF] Hosting environment: Production
2025-07-20 13:23:05.149 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:23:06.340 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:23:06.371 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:23:06.389 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:23:06.395 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 60.2781ms
2025-07-20 13:23:08.527 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=FlyeAYMK9mIxmrPWI7AVvw - null null
2025-07-20 13:23:08.536 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:23:08.593 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:23:08.595 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:47:46.645 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:47:46.692 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:47:46.699 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:47:46.713 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:47:46.715 +03:00 [INF] Initializing hub connection...
2025-07-20 13:47:48.873 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:47:48.967 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:47:48.972 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:47:48.977 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:47:48.979 +03:00 [INF] Hosting environment: Production
2025-07-20 13:47:48.984 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:47:49.174 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:47:49.218 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:47:49.248 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:47:49.256 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 90.211ms
2025-07-20 13:47:51.424 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=mR9jAl3DZKs8T_EmrijQTQ - null null
2025-07-20 13:47:51.432 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:47:51.498 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:47:51.500 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:49:36.116 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:49:36.168 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:49:36.175 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:49:36.191 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:49:36.193 +03:00 [INF] Initializing hub connection...
2025-07-20 13:49:38.597 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:49:38.700 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:49:38.703 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:49:38.708 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:49:38.710 +03:00 [INF] Hosting environment: Production
2025-07-20 13:49:38.712 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:49:39.091 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:49:39.145 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:49:39.171 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:49:39.176 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 92.2799ms
2025-07-20 13:49:41.362 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=8mhktnEJ8GLlCI_Dse5gcw - null null
2025-07-20 13:49:41.373 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:49:41.523 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:49:41.525 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:50:27.362 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:50:27.384 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:50:27.387 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:50:27.394 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:50:27.395 +03:00 [INF] Initializing hub connection...
2025-07-20 13:50:28.655 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:50:28.714 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:50:28.716 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:50:28.719 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:50:28.720 +03:00 [INF] Hosting environment: Production
2025-07-20 13:50:28.721 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:50:29.657 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:50:29.675 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:50:29.725 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:50:29.729 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 76.1698ms
2025-07-20 13:50:31.848 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=_zckueuwoPPU7Y--gH0tGw - null null
2025-07-20 13:50:31.857 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:50:31.922 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:50:31.924 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:52:05.176 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:52:05.206 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:52:05.211 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:52:05.220 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:52:05.220 +03:00 [INF] Initializing hub connection...
2025-07-20 13:52:06.166 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:52:06.248 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:52:06.249 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:52:06.251 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:52:06.252 +03:00 [INF] Hosting environment: Production
2025-07-20 13:52:06.253 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:52:07.417 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:52:07.440 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:52:07.453 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:52:07.456 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 41.6353ms
2025-07-20 13:52:09.594 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=uHKZoGtT5hCW4-fM9kdx1Q - null null
2025-07-20 13:52:09.602 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:52:09.739 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:52:09.741 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:52:17.944 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:52:17.948 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:52:17.950 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 5.3919ms
2025-07-20 13:52:17.953 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:52:17.955 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:52:17.956 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:52:17.967 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:52:18.148 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:52:18.226 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:58:04.919 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 13:58:04.947 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 13:58:04.950 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 13:58:04.961 +03:00 [INF] Capture image mode set to: false
2025-07-20 13:58:04.962 +03:00 [INF] Initializing hub connection...
2025-07-20 13:58:05.936 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 13:58:05.978 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 13:58:05.979 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 13:58:05.981 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 13:58:05.982 +03:00 [INF] Hosting environment: Production
2025-07-20 13:58:05.983 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 13:58:07.187 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:58:07.206 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:58:07.220 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:58:07.223 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 38.672ms
2025-07-20 13:58:09.332 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=9WWQk3d7StqNaWjtTJLhjA - null null
2025-07-20 13:58:09.339 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:58:09.384 +03:00 [INF] Successfully connected to Hub
2025-07-20 13:58:09.388 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 13:58:21.781 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:58:21.786 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:58:21.788 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 6.9ms
2025-07-20 13:58:21.791 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:58:21.793 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:58:21.794 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:58:21.804 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:58:21.969 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:58:22.000 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:58:52.222 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:58:52.254 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:58:52.286 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:58:52.273 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:58:52.288 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 33.8268ms
2025-07-20 13:58:52.289 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 499 null null 107.3379ms
2025-07-20 13:58:52.254 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:58:52.331 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-20 13:58:52.329 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:58:52.345 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:58:52.346 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=9WWQk3d7StqNaWjtTJLhjA - 101 null null 43014.5891ms
2025-07-20 13:58:52.347 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:58:52.347 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:58:52.350 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:58:52.351 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:58:52.351 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:58:52.361 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:58:52.368 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:58:52.376 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:58:54.193 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:58:57.949 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 13:58:57.957 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 13:58:57.958 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 13:58:57.958 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 9.8549ms
2025-07-20 13:58:57.984 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 13:58:57.987 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:58:58.051 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:58:58.056 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 5684.3289ms.
2025-07-20 13:58:58.063 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 13:58:58.067 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 5711.5704ms
2025-07-20 13:58:58.068 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:58:58.069 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 5740.5941ms
2025-07-20 13:59:04.923 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=qQQb5f2iGdFsAg-kqLA_UA - null null
2025-07-20 13:59:04.927 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 13:59:04.965 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 13:59:04.970 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:59:05.026 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:59:05.026 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 12658.0818ms.
2025-07-20 13:59:05.028 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 13:59:05.029 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 12676.8743ms
2025-07-20 13:59:05.030 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:59:05.031 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 12777.4789ms
2025-07-20 13:59:14.426 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:59:14.428 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:59:14.429 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.6386ms
2025-07-20 13:59:14.431 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:59:14.432 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:59:14.432 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:59:14.433 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:59:14.441 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:59:14.448 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:59:17.993 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 13:59:17.996 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:59:18.046 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:59:18.047 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 3604.8982ms.
2025-07-20 13:59:18.050 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 13:59:18.051 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 3617.3928ms
2025-07-20 13:59:18.053 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:59:18.054 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 3623.6617ms
2025-07-20 13:59:31.644 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 13:59:31.645 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:59:31.645 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 1.4932ms
2025-07-20 13:59:31.648 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 13:59:31.649 +03:00 [INF] CORS policy execution successful.
2025-07-20 13:59:31.649 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:59:31.650 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 13:59:31.659 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 13:59:31.677 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:59:36.254 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 13:59:36.257 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:59:36.310 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 13:59:36.311 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 4650.668ms.
2025-07-20 13:59:36.314 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 13:59:36.316 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 4665.0281ms
2025-07-20 13:59:36.319 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 13:59:36.320 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 4671.9368ms
2025-07-20 14:01:25.732 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:01:25.758 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:01:25.761 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:01:25.770 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:01:25.771 +03:00 [INF] Initializing hub connection...
2025-07-20 14:01:27.460 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:01:27.528 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:01:27.532 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:01:27.536 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:01:27.537 +03:00 [INF] Hosting environment: Production
2025-07-20 14:01:27.539 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:01:28.024 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:01:28.056 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:01:28.079 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:01:28.082 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 64.4659ms
2025-07-20 14:01:30.245 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=JcwPfn5LAZtoF1gWrfhf3g - null null
2025-07-20 14:01:30.250 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:01:30.338 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:01:30.340 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:01:37.482 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 14:01:37.485 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:01:37.488 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 5.843ms
2025-07-20 14:01:37.493 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 14:01:37.496 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:01:37.497 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:01:37.509 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 14:01:37.704 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 14:01:37.802 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:03:22.750 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:03:22.773 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:03:22.776 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:03:22.785 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:03:22.786 +03:00 [INF] Initializing hub connection...
2025-07-20 14:03:23.767 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:03:23.825 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:03:23.828 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:03:23.831 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:03:23.831 +03:00 [INF] Hosting environment: Production
2025-07-20 14:03:23.833 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:03:25.011 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:03:25.041 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:03:25.062 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:03:25.066 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 59.1339ms
2025-07-20 14:03:27.204 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=svIiu1Be_UVAAe5_Jedr4g - null null
2025-07-20 14:03:27.210 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:03:27.267 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:03:27.268 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:03:50.558 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 14:03:50.563 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:03:50.565 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 6.7656ms
2025-07-20 14:03:50.568 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 14:03:50.570 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:03:50.571 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:03:50.581 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 14:03:50.750 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 14:03:50.774 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:03:54.500 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 14:03:54.505 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:03:54.555 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:03:54.560 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 3805.0658ms.
2025-07-20 14:03:54.566 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 14:03:54.572 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 3986.263ms
2025-07-20 14:03:54.573 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:03:54.574 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 4005.7484ms
2025-07-20 14:04:02.824 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 14:04:02.826 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:04:02.827 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.7711ms
2025-07-20 14:04:02.830 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 14:04:02.832 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:04:02.833 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:04:02.834 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 14:04:02.844 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 14:04:02.854 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:10:39.347 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:10:39.377 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:10:39.380 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:10:39.391 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:10:39.393 +03:00 [INF] Initializing hub connection...
2025-07-20 14:10:40.347 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:10:40.386 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:10:40.388 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:10:40.390 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:10:40.391 +03:00 [INF] Hosting environment: Production
2025-07-20 14:10:40.391 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:10:41.648 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:10:41.669 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:10:41.696 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:10:41.699 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 54.6084ms
2025-07-20 14:10:43.835 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=3cO8Jw_BP86e7dWDCyBaMw - null null
2025-07-20 14:10:43.844 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:10:43.894 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:10:43.895 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:12:58.118 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 14:12:58.123 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:12:58.124 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 6.0642ms
2025-07-20 14:12:58.129 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 14:12:58.131 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:12:58.132 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:12:58.144 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 14:12:58.335 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 14:12:58.403 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:13:02.185 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 14:13:02.188 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:13:02.240 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:13:02.243 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 3904.8686ms.
2025-07-20 14:13:02.249 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 14:13:02.253 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 4104.0906ms
2025-07-20 14:13:02.254 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:13:02.255 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 4126.1711ms
2025-07-20 14:13:11.641 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 14:13:11.643 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:13:11.643 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.1698ms
2025-07-20 14:13:11.645 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 14:13:11.648 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:13:11.648 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:13:11.650 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 14:13:11.660 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 14:13:11.668 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:13:19.124 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 14:13:19.127 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:13:19.174 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:13:19.176 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 7514.8117ms.
2025-07-20 14:13:19.178 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 14:13:19.178 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 7526.6195ms
2025-07-20 14:13:19.179 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:13:19.180 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 7534.6756ms
2025-07-20 14:25:57.250 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:25:57.296 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:25:57.301 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:25:57.315 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:25:57.318 +03:00 [INF] Initializing hub connection...
2025-07-20 14:25:59.643 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:25:59.789 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:25:59.793 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:25:59.797 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:25:59.799 +03:00 [INF] Hosting environment: Production
2025-07-20 14:25:59.801 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:26:00.270 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:26:00.326 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:26:00.356 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:26:00.362 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 103.7403ms
2025-07-20 14:26:02.554 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=X9jC9Dx8Q14VmUKYdXzgVw - null null
2025-07-20 14:26:02.560 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:26:02.656 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:26:02.659 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:26:10.733 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 14:26:10.739 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:26:10.740 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.2905ms
2025-07-20 14:26:10.745 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 14:26:10.747 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:26:10.748 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:26:10.758 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 14:26:10.926 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 14:26:10.951 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:28:11.509 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:28:11.600 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:28:11.610 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:28:11.635 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:28:11.637 +03:00 [INF] Initializing hub connection...
2025-07-20 14:28:14.320 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:28:14.605 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:28:14.610 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:28:14.618 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:28:14.622 +03:00 [INF] Hosting environment: Production
2025-07-20 14:28:14.627 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:28:14.741 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:28:14.822 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:28:14.898 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:28:14.904 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 177.6384ms
2025-07-20 14:28:17.146 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=6Xf8ICYdAL-SuGDMeVZPKw - null null
2025-07-20 14:28:17.152 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:28:17.290 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:28:17.293 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:28:30.088 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 14:28:30.093 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:28:30.096 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.3084ms
2025-07-20 14:28:30.099 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 14:28:30.101 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:28:30.102 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:28:30.112 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 14:28:30.327 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 14:28:30.364 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:28:34.631 +03:00 [ERR] An error occurred during printing: PDF generation failed: One or more errors occurred. (Unable to load DLL 'libwkhtmltox' or one of its dependencies: The specified module could not be found. (0x8007007E))
2025-07-20 14:28:34.636 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:28:34.692 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:28:34.697 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 4363.7124ms.
2025-07-20 14:28:34.704 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 14:28:34.709 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 4592.975ms
2025-07-20 14:28:34.711 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:28:34.712 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 4612.4254ms
2025-07-20 14:28:40.729 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 14:28:40.731 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:28:40.732 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 2.8646ms
2025-07-20 14:28:40.734 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 14:28:40.737 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:28:40.737 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:28:40.738 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 14:28:40.749 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 14:28:40.755 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:30:15.962 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:30:15.989 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:30:15.993 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:30:16.002 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:30:16.003 +03:00 [INF] Initializing hub connection...
2025-07-20 14:30:19.733 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:30:19.786 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:30:19.788 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:30:19.793 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:30:19.794 +03:00 [INF] Hosting environment: Production
2025-07-20 14:30:19.794 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:30:20.198 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:30:20.226 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:30:20.246 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:30:20.250 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 57.4508ms
2025-07-20 14:30:22.366 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=WS0y58TcqCpaG2g2_XQ4Xw - null null
2025-07-20 14:30:22.371 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:30:22.510 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:30:22.523 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:30:28.461 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 14:30:28.466 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:30:28.469 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 8.0443ms
2025-07-20 14:30:28.474 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 14:30:28.476 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:30:28.477 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:30:28.488 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 14:30:31.988 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 14:30:32.026 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 14:38:23.822 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:38:23.912 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:38:23.919 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:38:23.946 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:38:23.947 +03:00 [INF] Initializing hub connection...
2025-07-20 14:38:25.568 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:38:25.681 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:38:25.685 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:38:25.690 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:38:25.691 +03:00 [INF] Hosting environment: Production
2025-07-20 14:38:25.693 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:38:26.468 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:38:26.507 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:38:26.536 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:38:26.542 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 81.4885ms
2025-07-20 14:38:28.193 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:38:28.195 +03:00 [INF] COM ports populated.
2025-07-20 14:38:28.724 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=mwbrrf4R3lW5UBo0eQU0-Q - null null
2025-07-20 14:38:28.730 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:38:28.818 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:38:28.820 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:38:28.897 +03:00 [ERR] Error opening COM port: Access to the path 'COM4' is denied.
2025-07-20 14:38:29.334 +03:00 [INF] No barcode scanner detected.
2025-07-20 14:38:29.337 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 14:38:29.339 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:38:29.432 +03:00 [INF] Found 4 tab pages from plugins
2025-07-20 14:38:29.465 +03:00 [INF] Total tabs in control: 4
2025-07-20 14:38:29.475 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:38:29.477 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:38:29.479 +03:00 [INF] Tab 2: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:38:29.480 +03:00 [INF] Tab 3: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 14:38:56.238 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:38:56.262 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:38:56.266 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:38:56.275 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:38:56.276 +03:00 [INF] Initializing hub connection...
2025-07-20 14:38:57.305 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:38:57.370 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:38:57.373 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:38:57.377 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:38:57.378 +03:00 [INF] Hosting environment: Production
2025-07-20 14:38:57.380 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:38:58.360 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:38:58.362 +03:00 [INF] COM ports populated.
2025-07-20 14:38:58.552 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:38:58.582 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:38:58.628 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:38:58.633 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 87.9162ms
2025-07-20 14:38:59.526 +03:00 [INF] No barcode scanner detected.
2025-07-20 14:38:59.532 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 14:38:59.534 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:38:59.593 +03:00 [INF] Found 4 tab pages from plugins
2025-07-20 14:38:59.623 +03:00 [INF] Total tabs in control: 4
2025-07-20 14:38:59.628 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:38:59.629 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:38:59.630 +03:00 [INF] Tab 2: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:38:59.632 +03:00 [INF] Tab 3: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 14:39:00.753 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=l3wjLtokdfD9yz98r63xsg - null null
2025-07-20 14:39:00.762 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:39:00.853 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:39:00.856 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:39:27.631 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:39:27.659 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:39:27.663 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:39:27.672 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:39:27.673 +03:00 [INF] Initializing hub connection...
2025-07-20 14:39:28.704 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:39:28.755 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:39:28.756 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:39:28.758 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:39:28.758 +03:00 [INF] Hosting environment: Production
2025-07-20 14:39:28.759 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:39:29.930 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:39:29.957 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:39:30.000 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:39:30.004 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 78.4026ms
2025-07-20 14:39:31.827 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:39:31.827 +03:00 [INF] COM ports populated.
2025-07-20 14:39:32.152 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=YDucljsV7nEWxQCJpbInOg - null null
2025-07-20 14:39:32.158 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:39:32.204 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:39:32.205 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:39:32.249 +03:00 [ERR] Error opening COM port: Access to the path 'COM4' is denied.
2025-07-20 14:39:32.980 +03:00 [INF] No barcode scanner detected.
2025-07-20 14:39:32.983 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 14:39:32.985 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:39:33.039 +03:00 [INF] Found 4 tab pages from plugins
2025-07-20 14:39:33.056 +03:00 [INF] Total tabs in control: 4
2025-07-20 14:39:33.063 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:39:33.064 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:39:33.065 +03:00 [INF] Tab 2: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:39:33.066 +03:00 [INF] Tab 3: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 14:40:20.774 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:40:20.805 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:40:20.808 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:40:20.818 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:40:20.819 +03:00 [INF] Initializing hub connection...
2025-07-20 14:40:21.741 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:40:21.791 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:40:21.794 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:40:21.796 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:40:21.796 +03:00 [INF] Hosting environment: Production
2025-07-20 14:40:21.797 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:40:23.112 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:40:23.136 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:40:23.152 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:40:23.155 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 47.563ms
2025-07-20 14:40:25.268 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ELC8E61zKD7wTZusRHhmAQ - null null
2025-07-20 14:40:25.271 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:40:25.330 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:40:25.332 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:40:50.104 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 14:40:50.109 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:40:50.112 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.4612ms
2025-07-20 14:40:50.117 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 14:40:50.119 +03:00 [INF] CORS policy execution successful.
2025-07-20 14:40:50.120 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:40:50.132 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 14:40:50.139 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 2.0605ms
2025-07-20 14:40:50.140 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 14:40:50.141 +03:00 [ERR] An unhandled exception has occurred while executing the request.
System.IO.FileNotFoundException: Could not load file or assembly 'DinkToPdf, Version=1.0.8.0, Culture=neutral, PublicKeyToken=null'. The system cannot find the file specified.
File name: 'DinkToPdf, Version=1.0.8.0, Culture=neutral, PublicKeyToken=null'
   at Mis.Agent.Print.PrintService..ctor(ILogger`1 logger)
   at InvokeStub_PrintService..ctor(Object, Span`1)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitDisposeCache(ServiceCallSite transientCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-20 14:40:50.204 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 86.3557ms
2025-07-20 14:40:52.763 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:40:52.764 +03:00 [INF] COM ports populated.
2025-07-20 14:40:52.787 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 14:40:52.788 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 14:40:52.789 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 14:40:52.790 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:40:52.824 +03:00 [INF] Found 4 tab pages from plugins
2025-07-20 14:40:52.839 +03:00 [INF] Total tabs in control: 4
2025-07-20 14:40:52.843 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:40:52.844 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:40:52.844 +03:00 [INF] Tab 2: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:40:52.845 +03:00 [INF] Tab 3: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 14:41:26.031 +03:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-07-20 14:41:26.225 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:41:26.318 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:41:26.322 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:41:26.328 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:41:26.330 +03:00 [INF] Hosting environment: Production
2025-07-20 14:41:26.331 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:42:31.045 +03:00 [INF] No action descriptors found. This may indicate an incorrectly configured application or missing application parts. To learn more, visit https://aka.ms/aspnet/mvc/app-parts
2025-07-20 14:42:31.681 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:42:31.959 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:42:31.965 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:42:31.981 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:42:31.987 +03:00 [INF] Hosting environment: Production
2025-07-20 14:42:31.991 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:43:31.107 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:43:31.131 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 14:43:31.137 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 14:43:31.971 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:43:32.030 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:43:32.032 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:43:32.034 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:43:32.036 +03:00 [INF] Hosting environment: Production
2025-07-20 14:43:32.037 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:43:47.261 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:43:47.263 +03:00 [INF] COM ports populated.
2025-07-20 14:43:48.406 +03:00 [INF] No barcode scanner detected.
2025-07-20 14:43:48.412 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-20 14:43:48.416 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:43:48.468 +03:00 [INF] Found 4 tab pages from plugins
2025-07-20 14:43:48.482 +03:00 [INF] Total tabs in control: 4
2025-07-20 14:43:48.487 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:43:48.488 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:43:48.488 +03:00 [INF] Tab 2: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:43:48.489 +03:00 [INF] Tab 3: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 14:44:25.178 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:44:25.201 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 14:44:25.218 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 14:44:25.818 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:44:25.858 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:44:25.863 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:44:25.867 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:44:25.869 +03:00 [INF] Hosting environment: Production
2025-07-20 14:44:25.871 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:45:07.672 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:45:07.700 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 14:45:07.703 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 14:45:17.837 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:45:17.884 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:45:17.886 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:45:17.889 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:45:17.891 +03:00 [INF] Hosting environment: Production
2025-07-20 14:45:17.893 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:45:31.922 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:45:31.923 +03:00 [INF] COM ports populated.
2025-07-20 14:45:33.058 +03:00 [INF] No barcode scanner detected.
2025-07-20 14:45:33.064 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-20 14:45:33.066 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:45:33.114 +03:00 [INF] Found 4 tab pages from plugins
2025-07-20 14:45:33.129 +03:00 [INF] Total tabs in control: 4
2025-07-20 14:45:33.133 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:45:33.133 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:45:33.134 +03:00 [INF] Tab 2: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:45:33.135 +03:00 [INF] Tab 3: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 14:47:03.659 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:47:03.687 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 14:47:03.692 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 14:47:04.338 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:47:04.391 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:47:04.394 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:47:04.397 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:47:04.398 +03:00 [INF] Hosting environment: Production
2025-07-20 14:47:04.399 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:47:08.874 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:47:08.876 +03:00 [INF] COM ports populated.
2025-07-20 14:47:10.013 +03:00 [INF] No barcode scanner detected.
2025-07-20 14:47:10.018 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-20 14:47:10.023 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:47:10.080 +03:00 [INF] Found 4 tab pages from plugins
2025-07-20 14:47:10.096 +03:00 [INF] Total tabs in control: 4
2025-07-20 14:47:10.101 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:47:10.103 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:47:10.104 +03:00 [INF] Tab 2: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:47:10.105 +03:00 [INF] Tab 3: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 14:47:42.419 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:47:42.496 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 14:47:42.504 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 14:47:44.077 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:47:44.202 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:47:44.206 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:47:44.213 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:47:44.217 +03:00 [INF] Hosting environment: Production
2025-07-20 14:47:44.220 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:47:49.952 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:47:49.953 +03:00 [INF] COM ports populated.
2025-07-20 14:47:51.085 +03:00 [INF] No barcode scanner detected.
2025-07-20 14:47:51.090 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-20 14:47:51.094 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:47:51.146 +03:00 [INF] Found 4 tab pages from plugins
2025-07-20 14:47:51.164 +03:00 [INF] Total tabs in control: 4
2025-07-20 14:47:51.168 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:47:51.169 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:47:51.170 +03:00 [INF] Tab 2: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:47:51.171 +03:00 [INF] Tab 3: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 14:51:02.514 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:51:02.543 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-20 14:51:02.547 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-20 14:51:13.979 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:51:14.043 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:51:14.045 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:51:14.048 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:51:14.049 +03:00 [INF] Hosting environment: Production
2025-07-20 14:51:14.050 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:51:18.425 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:51:18.427 +03:00 [INF] COM ports populated.
2025-07-20 14:51:19.587 +03:00 [INF] No barcode scanner detected.
2025-07-20 14:51:19.593 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-20 14:51:19.595 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:51:24.131 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 14:51:24.147 +03:00 [INF] Total tabs in control: 5
2025-07-20 14:51:24.153 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:51:24.155 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:51:24.157 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 14:51:24.158 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:51:24.159 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 14:51:30.448 +03:00 [INF] Port URL changed from 'https://127.0.0.1:7000' to 'https://127.0.0.1:7000/' - restart required
2025-07-20 14:54:27.378 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:54:27.413 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:54:27.417 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:54:27.433 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:54:27.435 +03:00 [INF] Initializing hub connection...
2025-07-20 14:54:30.100 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:54:30.198 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:54:30.203 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:54:30.210 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:54:30.213 +03:00 [INF] Hosting environment: Production
2025-07-20 14:54:30.215 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:54:30.402 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:54:30.459 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:54:30.489 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:54:30.496 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 101.9682ms
2025-07-20 14:54:32.689 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=uaoMIu2e2rkp-Z0Dtvw-Jw - null null
2025-07-20 14:54:32.694 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:54:32.759 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:54:32.763 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:54:35.626 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:54:35.627 +03:00 [INF] COM ports populated.
2025-07-20 14:54:35.692 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 14:54:35.696 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 14:54:35.697 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 14:54:35.698 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:54:35.874 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 14:54:35.903 +03:00 [INF] Total tabs in control: 5
2025-07-20 14:54:35.912 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:54:35.913 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:54:35.914 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 14:54:35.916 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:54:35.918 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 14:54:40.986 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:54:40.987 +03:00 [INF] COM ports populated.
2025-07-20 14:54:41.006 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 14:54:41.008 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 14:54:41.009 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 14:54:41.010 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:54:41.092 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 14:56:17.056 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:56:17.091 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:56:17.097 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:56:17.108 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:56:17.110 +03:00 [INF] Initializing hub connection...
2025-07-20 14:56:18.877 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:56:18.953 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:56:18.958 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:56:18.963 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:56:18.964 +03:00 [INF] Hosting environment: Production
2025-07-20 14:56:18.967 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:56:19.427 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:56:19.463 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:56:19.486 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:56:19.493 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 71.2976ms
2025-07-20 14:56:20.688 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:56:20.692 +03:00 [INF] COM ports populated.
2025-07-20 14:56:21.650 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=FzGuAEwXGJMcS1z4pF7vzg - null null
2025-07-20 14:56:21.659 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:56:21.710 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:56:21.714 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:56:21.766 +03:00 [ERR] Error opening COM port: Access to the path 'COM4' is denied.
2025-07-20 14:56:21.848 +03:00 [INF] No barcode scanner detected.
2025-07-20 14:56:21.858 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 14:56:21.867 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:56:22.061 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 14:56:22.068 +03:00 [INF] Total tabs in control: 5
2025-07-20 14:56:22.073 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:56:22.074 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:56:22.075 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 14:56:22.075 +03:00 [INF] Tab 3: Text=' إعدادات الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:56:22.076 +03:00 [INF] Tab 4: Text=' إعدادات الماسح الضوئي', Name='ScannerTab', Visible=False
2025-07-20 14:57:40.057 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:57:40.088 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:57:40.092 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:57:40.102 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:57:40.103 +03:00 [INF] Initializing hub connection...
2025-07-20 14:57:41.656 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:57:41.739 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:57:41.742 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:57:41.748 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:57:41.750 +03:00 [INF] Hosting environment: Production
2025-07-20 14:57:41.753 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:57:42.384 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:57:42.418 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:57:42.444 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:57:42.484 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 109.0166ms
2025-07-20 14:57:44.592 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=J6liCAtcE5uiuofrl2Bccg - null null
2025-07-20 14:57:44.601 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:57:44.652 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:57:44.655 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:57:44.977 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:57:44.978 +03:00 [INF] COM ports populated.
2025-07-20 14:57:45.017 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 14:57:45.018 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 14:57:45.019 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 14:57:45.020 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:57:45.197 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 14:57:45.206 +03:00 [INF] Total tabs in control: 5
2025-07-20 14:57:45.211 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:57:45.213 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:57:45.214 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 14:57:45.216 +03:00 [INF] Tab 3: Text=' إعدادات الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:57:45.217 +03:00 [INF] Tab 4: Text=' إعدادات الماسح الضوئي', Name='ScannerTab', Visible=False
2025-07-20 14:58:40.860 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 14:58:40.890 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 14:58:40.894 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 14:58:40.902 +03:00 [INF] Capture image mode set to: false
2025-07-20 14:58:40.904 +03:00 [INF] Initializing hub connection...
2025-07-20 14:58:42.648 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 14:58:42.727 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 14:58:42.734 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 14:58:42.740 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 14:58:42.743 +03:00 [INF] Hosting environment: Production
2025-07-20 14:58:42.746 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 14:58:43.160 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 14:58:43.202 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 14:58:43.223 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 14:58:43.229 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 73.8309ms
2025-07-20 14:58:45.379 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=7a7UBUeSWieNhGURRYEYqw - null null
2025-07-20 14:58:45.395 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 14:58:45.450 +03:00 [INF] Successfully connected to Hub
2025-07-20 14:58:45.457 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 14:59:03.160 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:59:03.163 +03:00 [INF] COM ports populated.
2025-07-20 14:59:03.183 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 14:59:03.185 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 14:59:03.186 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 14:59:03.188 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:59:09.465 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 14:59:09.486 +03:00 [INF] Total tabs in control: 5
2025-07-20 14:59:09.491 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 14:59:09.493 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 14:59:09.494 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 14:59:09.495 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 14:59:09.496 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 14:59:24.494 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 14:59:24.497 +03:00 [INF] COM ports populated.
2025-07-20 14:59:24.556 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 14:59:24.557 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 14:59:24.559 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 14:59:24.560 +03:00 [INF] TabPage returned successfully.
2025-07-20 14:59:43.921 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 14:59:59.751 +03:00 [ERR] Error loading plugin tabs: Cannot access a disposed object.
Object name: 'System.Windows.Forms.TabPage'.
2025-07-20 15:00:26.152 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:00:26.240 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:00:26.251 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:00:26.272 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:00:26.277 +03:00 [INF] Initializing hub connection...
2025-07-20 15:00:28.290 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:00:28.377 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:00:28.383 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:00:28.388 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:00:28.390 +03:00 [INF] Hosting environment: Production
2025-07-20 15:00:28.393 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:00:28.797 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:00:28.840 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:00:28.892 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:00:28.900 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 116.6544ms
2025-07-20 15:00:31.074 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=yhbJ-5FJNJXNcTzy_oMR1w - null null
2025-07-20 15:00:31.085 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:00:36.248 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:00:36.251 +03:00 [INF] COM ports populated.
2025-07-20 15:00:36.258 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:00:36.260 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:00:36.314 +03:00 [ERR] Error opening COM port: Access to the path 'COM4' is denied.
2025-07-20 15:00:37.388 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:00:37.389 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:00:37.390 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:00:37.516 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:00:46.640 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:00:46.647 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:00:46.648 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:00:46.649 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:00:46.650 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:00:46.651 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:00:51.311 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:00:51.311 +03:00 [INF] COM ports populated.
2025-07-20 15:00:52.480 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:00:52.483 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:00:52.486 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:00:52.585 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:03:12.427 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:03:12.456 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:03:12.461 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:03:12.478 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:03:12.480 +03:00 [INF] Initializing hub connection...
2025-07-20 15:03:13.681 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:03:13.743 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:03:13.748 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:03:13.755 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:03:13.758 +03:00 [INF] Hosting environment: Production
2025-07-20 15:03:13.762 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:03:14.774 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:03:14.803 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:03:14.827 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:03:14.875 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 63.598ms
2025-07-20 15:03:16.871 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:03:16.873 +03:00 [INF] COM ports populated.
2025-07-20 15:03:16.971 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=O2IQdNS2K6mNtizSyWS7cA - null null
2025-07-20 15:03:16.977 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:03:17.033 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:03:17.037 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:03:17.094 +03:00 [ERR] Error opening COM port: Access to the path 'COM4' is denied.
2025-07-20 15:03:18.023 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:03:18.026 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:03:18.028 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:03:18.280 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:03:18.320 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:03:18.330 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:03:18.331 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:03:18.333 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:03:18.335 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:03:18.336 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:03:22.718 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:03:22.719 +03:00 [INF] COM ports populated.
2025-07-20 15:03:23.883 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:03:23.887 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:03:23.890 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:03:23.978 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:04:57.344 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:04:57.407 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:04:57.416 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:04:57.438 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:04:57.442 +03:00 [INF] Initializing hub connection...
2025-07-20 15:04:59.548 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:04:59.634 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:04:59.639 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:04:59.644 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:04:59.647 +03:00 [INF] Hosting environment: Production
2025-07-20 15:04:59.649 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:04:59.895 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:04:59.933 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:04:59.957 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:04:59.994 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 105.8476ms
2025-07-20 15:05:02.092 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=T8dcI3gWj1SAsxJkO1nQzw - null null
2025-07-20 15:05:02.100 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:05:02.254 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:05:02.258 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:05:02.511 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:05:02.515 +03:00 [INF] COM ports populated.
2025-07-20 15:05:02.588 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:05:02.592 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:05:02.595 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:05:02.597 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:05:02.902 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:05:02.918 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:05:02.927 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:05:02.930 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:05:02.932 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:05:02.933 +03:00 [INF] Tab 3: Text=' إعدادات الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:05:02.935 +03:00 [INF] Tab 4: Text=' إعدادات الماسح الضوئي', Name='ScannerTab', Visible=False
2025-07-20 15:07:41.154 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:07:41.179 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:07:41.183 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:07:41.193 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:07:41.195 +03:00 [INF] Initializing hub connection...
2025-07-20 15:07:42.148 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:07:42.190 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:07:42.192 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:07:42.194 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:07:42.195 +03:00 [INF] Hosting environment: Production
2025-07-20 15:07:42.196 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:07:42.668 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:07:42.670 +03:00 [INF] COM ports populated.
2025-07-20 15:07:43.410 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:07:43.426 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:07:43.446 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:07:43.448 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 40.547ms
2025-07-20 15:07:43.811 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:07:43.814 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:07:43.815 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:07:44.051 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:07:44.064 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:07:44.070 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:07:44.071 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:07:44.072 +03:00 [INF] Tab 2: Text='Print', Name='', Visible=False
2025-07-20 15:07:44.072 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:07:44.073 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:07:45.586 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Eq6-KUi_35aaUGQgsIhxdA - null null
2025-07-20 15:07:45.595 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:07:45.635 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:07:45.638 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:13:53.387 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:13:53.413 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:13:53.419 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:13:53.431 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:13:53.432 +03:00 [INF] Initializing hub connection...
2025-07-20 15:13:54.419 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:13:54.468 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:13:54.470 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:13:54.477 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:13:54.478 +03:00 [INF] Hosting environment: Production
2025-07-20 15:13:54.480 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:13:55.672 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:13:55.694 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:13:55.707 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:13:55.710 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 41.5322ms
2025-07-20 15:13:57.823 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=GXt5o31yVRNBMbPD69vsYQ - null null
2025-07-20 15:13:57.827 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:13:57.866 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:13:57.867 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:14:00.675 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:14:00.680 +03:00 [INF] COM ports populated.
2025-07-20 15:14:00.714 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:14:00.715 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:14:00.717 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:14:00.718 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:14:01.014 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:14:01.032 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:14:01.037 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:14:01.038 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:14:01.038 +03:00 [INF] Tab 2: Text='Print', Name='', Visible=False
2025-07-20 15:14:01.039 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:14:01.040 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:15:00.420 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:15:00.445 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:15:00.448 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:15:00.458 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:15:00.460 +03:00 [INF] Initializing hub connection...
2025-07-20 15:15:01.549 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:15:01.612 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:15:01.615 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:15:01.621 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:15:01.623 +03:00 [INF] Hosting environment: Production
2025-07-20 15:15:01.625 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:15:02.681 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:15:02.701 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:15:02.756 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:15:02.775 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 81.2587ms
2025-07-20 15:15:04.890 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=4DDE6GUdoCW0Fq9hJFBB1g - null null
2025-07-20 15:15:04.897 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:15:04.995 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:15:04.998 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:15:08.157 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:15:08.159 +03:00 [INF] COM ports populated.
2025-07-20 15:15:08.194 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:15:08.197 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:15:08.198 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:15:08.199 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:15:08.339 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:15:08.361 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:15:08.367 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:15:08.368 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:15:08.372 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:15:08.373 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:15:08.374 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:15:28.002 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:15:28.003 +03:00 [INF] COM ports populated.
2025-07-20 15:15:28.023 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:15:28.025 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:15:28.026 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:15:28.027 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:15:28.112 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:17:22.766 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:17:22.790 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:17:22.793 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:17:22.801 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:17:22.802 +03:00 [INF] Initializing hub connection...
2025-07-20 15:17:23.784 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:17:23.831 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:17:23.835 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:17:23.838 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:17:23.839 +03:00 [INF] Hosting environment: Production
2025-07-20 15:17:23.840 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:17:25.030 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:17:25.051 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:17:25.069 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:17:25.071 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 44.6925ms
2025-07-20 15:17:27.154 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=eNcst6UWnfOElaE75dRehA - null null
2025-07-20 15:17:27.161 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:17:27.209 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:17:27.212 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:17:29.602 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:17:29.603 +03:00 [INF] COM ports populated.
2025-07-20 15:17:29.640 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:17:29.641 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:17:29.643 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:17:29.644 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:17:29.817 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:17:29.900 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:17:29.912 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:17:29.915 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:17:29.916 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:17:29.917 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:17:29.918 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:17:32.442 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:17:32.443 +03:00 [INF] COM ports populated.
2025-07-20 15:17:32.461 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:17:32.463 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:17:32.465 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:17:32.466 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:17:32.562 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:18:43.263 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:18:43.337 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:18:43.345 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:18:43.367 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:18:43.370 +03:00 [INF] Initializing hub connection...
2025-07-20 15:18:45.850 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:18:45.955 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:18:45.961 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:18:45.968 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:18:45.970 +03:00 [INF] Hosting environment: Production
2025-07-20 15:18:45.971 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:18:46.288 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:18:46.320 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:18:46.400 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:18:46.403 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 120.5594ms
2025-07-20 15:18:46.555 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:18:46.558 +03:00 [INF] COM ports populated.
2025-07-20 15:18:47.696 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:18:47.703 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:18:47.709 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:18:47.955 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:18:48.057 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:18:48.074 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:18:48.079 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:18:48.081 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:18:48.082 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:18:48.083 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:18:48.585 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=RN1UPfvSL46-ZSGx9Qh1cw - null null
2025-07-20 15:18:48.592 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:18:48.665 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:18:48.667 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:18:52.815 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:18:52.816 +03:00 [INF] COM ports populated.
2025-07-20 15:18:52.843 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:18:52.846 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:18:52.846 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:18:52.847 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:18:52.929 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:22:46.816 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:22:46.838 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:22:46.841 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:22:46.851 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:22:46.853 +03:00 [INF] Initializing hub connection...
2025-07-20 15:22:47.797 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:22:47.861 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:22:47.865 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:22:47.868 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:22:47.870 +03:00 [INF] Hosting environment: Production
2025-07-20 15:22:47.871 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:22:49.077 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:22:49.103 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:22:49.117 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:22:49.120 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 46.0816ms
2025-07-20 15:22:51.214 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=BIBu61E29Vtd2mBGFUKThg - null null
2025-07-20 15:22:51.226 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:22:51.308 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:22:51.311 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:23:02.260 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:23:02.262 +03:00 [INF] COM ports populated.
2025-07-20 15:23:02.299 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:23:02.302 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:23:02.304 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:23:02.305 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:23:02.484 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:23:04.669 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:23:04.693 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:23:04.697 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:23:04.699 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:23:04.702 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:23:04.705 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:23:09.533 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:23:09.535 +03:00 [INF] COM ports populated.
2025-07-20 15:23:09.601 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:23:09.603 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:23:09.604 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:23:09.606 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:23:09.796 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:24:21.171 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:24:21.240 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:24:21.248 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:24:21.270 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:24:21.273 +03:00 [INF] Initializing hub connection...
2025-07-20 15:24:23.268 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:24:23.352 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:24:23.357 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:24:23.363 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:24:23.367 +03:00 [INF] Hosting environment: Production
2025-07-20 15:24:23.369 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:24:23.713 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:24:23.754 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:24:23.780 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:24:23.788 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 80.5988ms
2025-07-20 15:24:25.064 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:24:25.069 +03:00 [INF] COM ports populated.
2025-07-20 15:24:25.941 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=O9Cz1zhXOWnqaHbTCI0hFg - null null
2025-07-20 15:24:25.953 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:24:26.071 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:24:26.074 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:24:26.264 +03:00 [ERR] Error opening COM port: Access to the path 'COM4' is denied.
2025-07-20 15:24:26.330 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:24:26.334 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:24:26.337 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:24:26.676 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:24:37.233 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:24:37.242 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:24:37.244 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:24:37.245 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:24:37.247 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:24:37.248 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:25:00.243 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:25:00.244 +03:00 [INF] COM ports populated.
2025-07-20 15:25:01.369 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:25:01.374 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:25:01.379 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:25:01.470 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:29:29.009 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:29:29.031 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:29:29.034 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:29:29.042 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:29:29.043 +03:00 [INF] Initializing hub connection...
2025-07-20 15:29:32.132 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:29:32.178 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:29:32.182 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:29:32.185 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:29:32.186 +03:00 [INF] Hosting environment: Production
2025-07-20 15:29:32.187 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:29:32.669 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:29:32.694 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:29:32.710 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:29:32.712 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 47.1402ms
2025-07-20 15:29:34.826 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=97HHP1Zmx7skuuSYYJ7icw - null null
2025-07-20 15:29:34.829 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:29:34.901 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:29:34.904 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:29:34.963 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:29:34.965 +03:00 [INF] COM ports populated.
2025-07-20 15:29:34.994 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:29:34.999 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:29:35.000 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:29:35.002 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:29:40.926 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:29:40.971 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:29:40.980 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:29:40.981 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:29:40.983 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:29:40.983 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:29:40.984 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:32:11.261 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:32:11.361 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:32:11.375 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:32:11.404 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:32:11.409 +03:00 [INF] Initializing hub connection...
2025-07-20 15:32:16.329 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:32:16.423 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:32:16.427 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:32:16.431 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:32:16.434 +03:00 [INF] Hosting environment: Production
2025-07-20 15:32:16.437 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:32:16.838 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:32:16.878 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:32:16.902 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:32:16.906 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 77.6412ms
2025-07-20 15:32:17.672 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:32:17.674 +03:00 [INF] COM ports populated.
2025-07-20 15:32:18.810 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:32:18.814 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:32:18.817 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:32:19.916 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=3Mtpys7KD1O-1sHmTMhgeA - null null
2025-07-20 15:32:19.925 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:32:20.018 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:32:20.020 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:32:20.182 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:32:20.189 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:32:20.195 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:32:20.196 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:32:20.197 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:32:20.198 +03:00 [INF] Tab 3: Text=' إعدادات الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:32:20.199 +03:00 [INF] Tab 4: Text=' إعدادات الماسح الضوئي', Name='ScannerTab', Visible=False
2025-07-20 15:33:50.912 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:33:50.935 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:33:50.938 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:33:50.948 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:33:50.949 +03:00 [INF] Initializing hub connection...
2025-07-20 15:33:53.393 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:33:53.434 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:33:53.436 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:33:53.439 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:33:53.441 +03:00 [INF] Hosting environment: Production
2025-07-20 15:33:53.442 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:33:53.734 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:33:53.754 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:33:53.767 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:33:53.770 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 38.6558ms
2025-07-20 15:33:55.584 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:33:55.585 +03:00 [INF] COM ports populated.
2025-07-20 15:33:55.862 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=S75FnE0jEX7pBiM7FaXlTw - null null
2025-07-20 15:33:55.871 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:33:55.907 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:33:55.914 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:33:55.958 +03:00 [ERR] Error opening COM port: Access to the path 'COM4' is denied.
2025-07-20 15:33:56.731 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:33:56.733 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:33:56.737 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:34:02.689 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:34:12.452 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:34:12.459 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:34:12.462 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:34:12.464 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:34:12.465 +03:00 [INF] Tab 3: Text=' إعدادات الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:34:12.467 +03:00 [INF] Tab 4: Text=' إعدادات الماسح الضوئي', Name='ScannerTab', Visible=False
2025-07-20 15:34:25.094 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:34:25.096 +03:00 [INF] COM ports populated.
2025-07-20 15:34:26.228 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:34:26.233 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:34:26.235 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:34:26.328 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:34:29.715 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:34:29.718 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:34:29.719 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:34:29.720 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:34:29.721 +03:00 [INF] Tab 3: Text=' إعدادات الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:34:29.723 +03:00 [INF] Tab 4: Text=' إعدادات الماسح الضوئي', Name='ScannerTab', Visible=False
2025-07-20 15:36:19.026 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:36:19.098 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:36:19.109 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:36:19.129 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:36:19.133 +03:00 [INF] Initializing hub connection...
2025-07-20 15:36:21.425 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:36:21.499 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:36:21.502 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:36:21.505 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:36:21.507 +03:00 [INF] Hosting environment: Production
2025-07-20 15:36:21.509 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:36:21.593 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:36:21.635 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:36:21.660 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:36:21.666 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 80.5473ms
2025-07-20 15:36:23.861 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ZN7nM_ENwV40cor-cvRUXg - null null
2025-07-20 15:36:23.872 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:36:23.980 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:36:23.983 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:36:26.958 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:36:26.959 +03:00 [INF] COM ports populated.
2025-07-20 15:36:26.989 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:36:26.991 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:36:26.992 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:36:26.994 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:36:27.137 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:36:30.233 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:36:30.248 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:36:30.249 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:36:30.250 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:36:30.252 +03:00 [INF] Tab 3: Text=' إعدادات الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:36:30.253 +03:00 [INF] Tab 4: Text=' إعدادات الماسح الضوئي', Name='ScannerTab', Visible=False
2025-07-20 15:36:49.529 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:36:49.554 +03:00 [INF] COM ports populated.
2025-07-20 15:36:49.584 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:36:49.586 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:36:49.588 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:36:49.589 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:36:49.665 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:36:53.082 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:36:53.088 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:36:53.089 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:36:53.091 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:36:53.092 +03:00 [INF] Tab 3: Text=' إعدادات الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:36:53.093 +03:00 [INF] Tab 4: Text=' إعدادات الماسح الضوئي', Name='ScannerTab', Visible=False
2025-07-20 15:38:59.493 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:38:59.544 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:38:59.551 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:38:59.568 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:38:59.570 +03:00 [INF] Initializing hub connection...
2025-07-20 15:39:03.720 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:39:03.882 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:39:03.889 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:39:03.898 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:39:03.901 +03:00 [INF] Hosting environment: Production
2025-07-20 15:39:03.904 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:39:04.038 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:39:04.116 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:39:04.169 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:39:04.181 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 158.1793ms
2025-07-20 15:39:06.483 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=lA0mAaQsON82N-NFr6h48g - null null
2025-07-20 15:39:06.501 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:39:06.667 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:39:06.672 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:39:13.597 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:39:13.599 +03:00 [INF] COM ports populated.
2025-07-20 15:39:13.641 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:39:13.645 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:39:13.646 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:39:13.648 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:39:15.967 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:39:16.017 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:39:16.024 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:39:16.025 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:39:16.026 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:39:16.027 +03:00 [INF] Tab 3: Text=' إعدادات الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:39:16.028 +03:00 [INF] Tab 4: Text=' إعدادات الماسح الضوئي', Name='ScannerTab', Visible=False
2025-07-20 15:39:19.758 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:39:19.760 +03:00 [INF] COM ports populated.
2025-07-20 15:39:19.778 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:39:19.780 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:39:19.781 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:39:19.782 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:39:21.394 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:39:21.405 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:39:21.409 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:39:21.410 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:39:21.413 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:39:21.414 +03:00 [INF] Tab 3: Text=' إعدادات الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:39:21.415 +03:00 [INF] Tab 4: Text=' إعدادات الماسح الضوئي', Name='ScannerTab', Visible=False
2025-07-20 15:42:02.818 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:42:02.840 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:42:02.843 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:42:02.852 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:42:02.854 +03:00 [INF] Initializing hub connection...
2025-07-20 15:42:03.951 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:42:04.015 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:42:04.020 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:42:04.024 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:42:04.026 +03:00 [INF] Hosting environment: Production
2025-07-20 15:42:04.028 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:42:05.070 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:42:05.112 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:42:05.132 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:42:05.135 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 69.4802ms
2025-07-20 15:42:07.266 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=5rheM_Hqo6kEQ10WdM0CaQ - null null
2025-07-20 15:42:07.272 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:42:07.313 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:42:07.317 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:42:44.498 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:42:44.499 +03:00 [INF] COM ports populated.
2025-07-20 15:42:44.523 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:42:44.524 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:42:44.525 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:42:44.526 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:42:47.349 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:42:53.555 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:42:53.562 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:42:53.564 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:42:53.566 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:42:53.567 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:42:53.568 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:43:07.726 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:43:07.727 +03:00 [INF] COM ports populated.
2025-07-20 15:43:07.745 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:43:07.746 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:43:07.747 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:43:07.748 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:43:11.076 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:43:26.511 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:43:26.516 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:43:26.518 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:43:26.519 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:43:26.520 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:43:26.521 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:43:32.506 +03:00 [ERR]  An error occurred in RunTabsFormAsync: Cannot access a disposed object.
Object name: 'System.Windows.Forms.TabPage'.
2025-07-20 15:43:36.410 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:43:36.411 +03:00 [INF] COM ports populated.
2025-07-20 15:43:36.428 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:43:36.429 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:43:36.430 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:43:36.431 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:43:36.611 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:43:39.549 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:43:39.552 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:43:39.553 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:43:39.555 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:43:39.556 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:43:39.557 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:43:40.812 +03:00 [ERR]  An error occurred in RunTabsFormAsync: Cannot access a disposed object.
Object name: 'System.Windows.Forms.TabPage'.
2025-07-20 15:43:51.513 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:43:51.514 +03:00 [INF] COM ports populated.
2025-07-20 15:43:51.529 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:43:51.531 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:43:51.531 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:43:51.532 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:43:51.724 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:43:51.732 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:43:51.763 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:43:51.772 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:43:51.774 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:43:51.775 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:43:51.776 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:43:52.817 +03:00 [ERR]  An error occurred in RunTabsFormAsync: Cannot access a disposed object.
Object name: 'System.Windows.Forms.TabPage'.
2025-07-20 15:44:25.744 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:44:25.746 +03:00 [INF] COM ports populated.
2025-07-20 15:44:25.763 +03:00 [ERR] Access to the port COM4 is denied: Access to the path 'COM4' is denied.
2025-07-20 15:44:25.764 +03:00 [INF] Barcode scanner connected on COM4
2025-07-20 15:44:25.766 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:44:25.767 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:44:25.983 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:44:25.993 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:44:25.996 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:44:25.997 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:44:25.999 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:44:26.000 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:44:26.002 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:46:03.053 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:46:03.077 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:46:03.081 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:46:03.089 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:46:03.091 +03:00 [INF] Initializing hub connection...
2025-07-20 15:46:03.953 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:46:03.995 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:46:03.998 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:46:04.000 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:46:04.002 +03:00 [INF] Hosting environment: Production
2025-07-20 15:46:04.003 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:46:05.314 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:46:05.332 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:46:05.346 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:46:05.350 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 37.9096ms
2025-07-20 15:46:08.989 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=G_OchrgNVfIYA3NrH3E-pg - null null
2025-07-20 15:46:09.074 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:46:09.103 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:46:09.105 +03:00 [INF] COM ports populated.
2025-07-20 15:46:09.148 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:46:09.151 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:46:09.222 +03:00 [ERR] Error opening COM port: Access to the path 'COM4' is denied.
2025-07-20 15:46:10.309 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:46:10.312 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:46:10.314 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:46:10.908 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:46:10.928 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:46:10.936 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:46:10.937 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:46:10.939 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:46:10.940 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:46:10.941 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:46:18.481 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 15:46:18.483 +03:00 [INF] COM ports populated.
2025-07-20 15:46:19.601 +03:00 [INF] No barcode scanner detected.
2025-07-20 15:46:19.602 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 15:46:19.603 +03:00 [INF] TabPage returned successfully.
2025-07-20 15:46:20.222 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 15:46:20.234 +03:00 [INF] Total tabs in control: 5
2025-07-20 15:46:20.236 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 15:46:20.237 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 15:46:20.238 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 15:46:20.238 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 15:46:20.239 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 15:48:02.841 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 15:48:02.851 +03:00 [INF] CORS policy execution successful.
2025-07-20 15:48:02.853 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 11.9396ms
2025-07-20 15:48:02.858 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 15:48:02.861 +03:00 [INF] CORS policy execution successful.
2025-07-20 15:48:02.863 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 15:48:02.874 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 15:48:03.054 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 15:53:36.339 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:53:36.366 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:53:36.371 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:53:36.380 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:53:36.382 +03:00 [INF] Initializing hub connection...
2025-07-20 15:53:37.311 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:53:37.353 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:53:37.359 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:53:37.362 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:53:37.363 +03:00 [INF] Hosting environment: Production
2025-07-20 15:53:37.365 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:53:38.612 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:53:38.636 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:53:38.651 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:53:38.655 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 45.7016ms
2025-07-20 15:53:40.772 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=aDojxB8s2JBNBoFhab4jsg - null null
2025-07-20 15:53:40.779 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:53:40.823 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:53:40.825 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:53:43.611 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 15:53:43.619 +03:00 [INF] CORS policy execution successful.
2025-07-20 15:53:43.621 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 9.8529ms
2025-07-20 15:53:43.626 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 15:53:43.632 +03:00 [INF] CORS policy execution successful.
2025-07-20 15:53:43.633 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 15:53:43.645 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 15:53:43.858 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 15:53:43.887 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 15:54:00.527 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:54:00.550 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:54:00.554 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:54:00.564 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:54:00.565 +03:00 [INF] Initializing hub connection...
2025-07-20 15:54:01.807 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:54:01.889 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:54:01.891 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:54:01.894 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:54:01.895 +03:00 [INF] Hosting environment: Production
2025-07-20 15:54:01.897 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:54:02.802 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:54:02.830 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:54:02.847 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:54:02.851 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 52.9814ms
2025-07-20 15:54:04.959 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=RhbGxfy1qjnmGHmta1SQyg - null null
2025-07-20 15:54:04.964 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:54:05.010 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:54:05.013 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:54:17.660 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 15:54:17.667 +03:00 [INF] CORS policy execution successful.
2025-07-20 15:54:17.669 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 9.6193ms
2025-07-20 15:54:17.673 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 15:54:17.675 +03:00 [INF] CORS policy execution successful.
2025-07-20 15:54:17.676 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 15:54:17.687 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 15:54:17.865 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 15:54:18.028 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 15:55:37.604 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:55:37.628 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:55:37.633 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:55:37.644 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:55:37.646 +03:00 [INF] Initializing hub connection...
2025-07-20 15:55:38.586 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:55:38.626 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:55:38.636 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:55:38.638 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:55:38.639 +03:00 [INF] Hosting environment: Production
2025-07-20 15:55:38.641 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:55:39.853 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:55:39.872 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:55:39.887 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:55:39.898 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 46.5559ms
2025-07-20 15:55:42.006 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Jfbyn6nnJ6T29MnYlqW_vg - null null
2025-07-20 15:55:42.011 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:55:42.052 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:55:42.056 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:55:58.874 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 15:55:58.883 +03:00 [INF] CORS policy execution successful.
2025-07-20 15:55:58.885 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 11.6388ms
2025-07-20 15:55:58.890 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 15:55:58.894 +03:00 [INF] CORS policy execution successful.
2025-07-20 15:55:58.895 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 15:55:58.907 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 15:55:59.091 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 15:55:59.117 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 15:57:08.538 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 15:57:08.591 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 15:57:08.598 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 15:57:08.614 +03:00 [INF] Capture image mode set to: false
2025-07-20 15:57:08.616 +03:00 [INF] Initializing hub connection...
2025-07-20 15:57:10.826 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 15:57:10.924 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 15:57:10.930 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 15:57:10.938 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 15:57:10.940 +03:00 [INF] Hosting environment: Production
2025-07-20 15:57:10.943 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 15:57:11.006 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 15:57:11.050 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 15:57:11.083 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 15:57:11.091 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 92.7117ms
2025-07-20 15:57:13.261 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=FCyCUKCS9q-4ASUIJiIzHQ - null null
2025-07-20 15:57:13.268 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 15:57:13.348 +03:00 [INF] Successfully connected to Hub
2025-07-20 15:57:13.353 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 15:57:19.359 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 15:57:19.366 +03:00 [INF] CORS policy execution successful.
2025-07-20 15:57:19.368 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 8.6938ms
2025-07-20 15:57:19.372 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 15:57:19.377 +03:00 [INF] CORS policy execution successful.
2025-07-20 15:57:19.378 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 15:57:19.388 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 15:57:19.554 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 15:57:19.585 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:00:22.266 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 16:00:22.290 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 16:00:22.294 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 16:00:22.303 +03:00 [INF] Capture image mode set to: false
2025-07-20 16:00:22.304 +03:00 [INF] Initializing hub connection...
2025-07-20 16:00:23.249 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 16:00:23.301 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 16:00:23.303 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 16:00:23.305 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:00:23.306 +03:00 [INF] Hosting environment: Production
2025-07-20 16:00:23.307 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 16:00:24.541 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 16:00:24.565 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 16:00:24.584 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 16:00:24.587 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 50.1799ms
2025-07-20 16:00:26.684 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=kwRg0NuAtGy6aEbx-QyJbA - null null
2025-07-20 16:00:26.715 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 16:00:26.827 +03:00 [INF] Successfully connected to Hub
2025-07-20 16:00:26.830 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 16:00:35.811 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 16:00:35.817 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:00:35.818 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.8686ms
2025-07-20 16:00:35.824 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 16:00:35.830 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:00:35.832 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 16:00:35.846 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 16:00:36.022 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 16:00:36.041 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:02:24.802 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 16:02:24.827 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 16:02:24.832 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 16:02:24.841 +03:00 [INF] Capture image mode set to: false
2025-07-20 16:02:24.843 +03:00 [INF] Initializing hub connection...
2025-07-20 16:02:25.778 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 16:02:25.817 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 16:02:25.820 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 16:02:25.824 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:02:25.826 +03:00 [INF] Hosting environment: Production
2025-07-20 16:02:25.827 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 16:02:27.107 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 16:02:27.127 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 16:02:27.143 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 16:02:27.161 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 42.0366ms
2025-07-20 16:02:29.222 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=dYRJtR4dCDqRg21QkPMGvw - null null
2025-07-20 16:02:29.228 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 16:02:29.266 +03:00 [INF] Successfully connected to Hub
2025-07-20 16:02:29.268 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 16:02:32.902 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 16:02:32.909 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:02:32.912 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 9.6325ms
2025-07-20 16:02:32.918 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 16:02:32.922 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:02:32.923 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 16:02:32.944 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 16:02:33.149 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 16:02:33.173 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:22:40.954 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 16:22:40.980 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 16:22:40.986 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 16:22:40.996 +03:00 [INF] Capture image mode set to: false
2025-07-20 16:22:40.998 +03:00 [INF] Initializing hub connection...
2025-07-20 16:22:42.034 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 16:22:42.125 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 16:22:42.129 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 16:22:42.131 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:22:42.133 +03:00 [INF] Hosting environment: Production
2025-07-20 16:22:42.134 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 16:22:43.230 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 16:22:43.246 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 16:22:43.268 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 16:22:43.271 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 43.7376ms
2025-07-20 16:22:45.346 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=hWZarPi29CjKbxxLVRVpzw - null null
2025-07-20 16:22:45.355 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 16:22:45.398 +03:00 [INF] Successfully connected to Hub
2025-07-20 16:22:45.400 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 16:22:50.518 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 16:22:50.524 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:22:50.526 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 7.5302ms
2025-07-20 16:22:50.531 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 16:22:50.535 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:22:50.537 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 16:22:50.549 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 16:22:50.746 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 16:22:50.804 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:22:58.611 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 16:22:58.614 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:22:58.721 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:22:58.725 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 7973.2433ms.
2025-07-20 16:22:58.732 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 16:22:58.737 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 8180.8683ms
2025-07-20 16:22:58.739 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 16:22:58.741 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 8209.9294ms
2025-07-20 16:29:27.693 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 16:29:27.717 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 16:29:27.722 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 16:29:27.732 +03:00 [INF] Capture image mode set to: false
2025-07-20 16:29:27.733 +03:00 [INF] Initializing hub connection...
2025-07-20 16:29:28.954 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 16:29:28.998 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 16:29:29.002 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 16:29:29.004 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:29:29.006 +03:00 [INF] Hosting environment: Production
2025-07-20 16:29:29.007 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 16:29:29.955 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 16:29:29.973 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 16:29:30.025 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 16:29:30.029 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 76.4152ms
2025-07-20 16:29:32.126 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ynv4tV5K2nc-GvmbC5I2Jg - null null
2025-07-20 16:29:32.132 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 16:29:32.171 +03:00 [INF] Successfully connected to Hub
2025-07-20 16:29:32.174 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 16:29:37.029 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 16:29:37.035 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:29:37.038 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 9.1663ms
2025-07-20 16:29:37.042 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 16:29:37.045 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:29:37.046 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 16:29:37.056 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 16:29:37.221 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 16:29:37.251 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:30:25.603 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 16:30:25.628 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 16:30:25.632 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 16:30:25.641 +03:00 [INF] Capture image mode set to: false
2025-07-20 16:30:25.644 +03:00 [INF] Initializing hub connection...
2025-07-20 16:30:26.905 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 16:30:26.947 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 16:30:26.951 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 16:30:26.954 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:30:26.956 +03:00 [INF] Hosting environment: Production
2025-07-20 16:30:26.957 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 16:30:27.864 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 16:30:27.887 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 16:30:27.901 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 16:30:27.903 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 42.5334ms
2025-07-20 16:30:30.016 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=FBBGZASDrS5PXw5OdOqVcA - null null
2025-07-20 16:30:30.022 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 16:30:30.073 +03:00 [INF] Successfully connected to Hub
2025-07-20 16:30:30.076 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 16:30:39.469 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 16:30:39.475 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:30:39.477 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 8.0133ms
2025-07-20 16:30:39.481 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 16:30:39.483 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:30:39.483 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 16:30:39.493 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 16:30:39.660 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 16:30:39.719 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:30:51.546 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 16:30:51.551 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:30:51.625 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:30:51.631 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 11962.3405ms.
2025-07-20 16:30:51.639 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 16:30:51.645 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 12147.8231ms
2025-07-20 16:30:51.647 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 16:30:51.649 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 12167.6771ms
2025-07-20 16:30:56.263 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 16:30:56.265 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:30:56.266 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 3.0268ms
2025-07-20 16:30:56.269 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 16:30:56.271 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:30:56.271 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 16:30:56.272 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 16:30:56.283 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 16:30:56.293 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:33:33.505 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 16:33:33.530 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 16:33:33.534 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 16:33:33.544 +03:00 [INF] Capture image mode set to: false
2025-07-20 16:33:33.546 +03:00 [INF] Initializing hub connection...
2025-07-20 16:33:34.708 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 16:33:34.750 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 16:33:34.753 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 16:33:34.755 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:33:34.756 +03:00 [INF] Hosting environment: Production
2025-07-20 16:33:34.758 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 16:33:35.771 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 16:33:35.790 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 16:33:35.805 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 16:33:35.823 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 40.1611ms
2025-07-20 16:33:37.890 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=q59NvCLsRt-oNwcmfk5keQ - null null
2025-07-20 16:33:37.900 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 16:33:37.940 +03:00 [INF] Successfully connected to Hub
2025-07-20 16:33:37.943 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 16:33:43.265 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 16:33:43.274 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:33:43.276 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 11.2551ms
2025-07-20 16:33:43.280 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 16:33:43.282 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:33:43.283 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 16:33:43.292 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 16:33:43.456 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 16:33:43.512 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:34:09.534 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 16:34:09.559 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 16:34:09.564 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 16:34:09.574 +03:00 [INF] Capture image mode set to: false
2025-07-20 16:34:09.575 +03:00 [INF] Initializing hub connection...
2025-07-20 16:34:10.661 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 16:34:10.701 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 16:34:10.706 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 16:34:10.710 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:34:10.711 +03:00 [INF] Hosting environment: Production
2025-07-20 16:34:10.712 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 16:34:11.800 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 16:34:11.821 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 16:34:11.837 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 16:34:11.856 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 43.3785ms
2025-07-20 16:34:13.940 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=oU6gT-bvxlok_wl820AmCA - null null
2025-07-20 16:34:13.942 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 16:34:14.009 +03:00 [INF] Successfully connected to Hub
2025-07-20 16:34:14.012 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 16:34:18.213 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 16:34:18.218 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:34:18.219 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 6.9197ms
2025-07-20 16:34:18.223 +03:00 [INF] Request starting HTTP/2 POST https://localhost:7000/Print/PrintAsync - application/json; charset=utf-8 74706
2025-07-20 16:34:18.226 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:34:18.227 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 16:34:18.236 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-20 16:34:18.408 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-20 16:34:18.477 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:34:33.199 +03:00 [ERR] An error occurred during printing: Can not parse a PDF from an empty byte array.
2025-07-20 16:34:33.202 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:34:33.263 +03:00 [WRN] AgentForm or TabControl not available for refresh.
2025-07-20 16:34:33.268 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 14854.478ms.
2025-07-20 16:34:33.275 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-20 16:34:33.279 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 15039.1324ms
2025-07-20 16:34:33.281 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-20 16:34:33.282 +03:00 [INF] Request finished HTTP/2 POST https://localhost:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 15058.9488ms
2025-07-20 16:49:51.213 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 16:49:51.399 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 16:49:51.419 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 16:49:51.481 +03:00 [INF] Capture image mode set to: false
2025-07-20 16:49:51.483 +03:00 [INF] Initializing hub connection...
2025-07-20 16:49:55.891 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 16:49:56.005 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 16:49:56.011 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 16:49:56.017 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:49:56.021 +03:00 [INF] Hosting environment: Production
2025-07-20 16:49:56.024 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 16:49:56.214 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 16:49:56.268 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 16:49:56.298 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 16:49:56.306 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 99.6886ms
2025-07-20 16:49:58.471 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=NF-fU2qWDAhmNBSjsAnTSw - null null
2025-07-20 16:49:58.479 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 16:49:58.665 +03:00 [INF] Successfully connected to Hub
2025-07-20 16:49:58.670 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 16:50:06.741 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - null null
2025-07-20 16:50:06.753 +03:00 [INF] CORS policy execution successful.
2025-07-20 16:50:06.756 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Print/PrintAsync - 204 null null 15.315ms
2025-07-20 16:50:22.829 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 16:50:22.895 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 16:50:22.905 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 16:50:22.927 +03:00 [INF] Capture image mode set to: false
2025-07-20 16:50:22.930 +03:00 [INF] Initializing hub connection...
2025-07-20 16:50:25.336 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 16:50:25.421 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 16:50:25.426 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 16:50:25.431 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:50:25.433 +03:00 [INF] Hosting environment: Production
2025-07-20 16:50:25.435 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 16:50:25.951 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 16:50:25.998 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 16:50:26.026 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 16:50:26.033 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 88.7439ms
2025-07-20 16:50:27.584 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 16:50:27.587 +03:00 [INF] COM ports populated.
2025-07-20 16:50:28.170 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ESsqpNlSxFocK3dICZOF5w - null null
2025-07-20 16:50:28.179 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 16:50:28.283 +03:00 [INF] Successfully connected to Hub
2025-07-20 16:50:28.286 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 16:50:28.384 +03:00 [ERR] Error opening COM port: Access to the path 'COM4' is denied.
2025-07-20 16:50:28.736 +03:00 [INF] No barcode scanner detected.
2025-07-20 16:50:28.738 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 16:50:28.741 +03:00 [INF] TabPage returned successfully.
2025-07-20 16:50:29.734 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 16:50:29.770 +03:00 [INF] Total tabs in control: 5
2025-07-20 16:50:29.783 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 16:50:29.786 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 16:50:29.787 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 16:50:29.788 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 16:50:29.790 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 16:50:46.379 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 16:50:46.381 +03:00 [INF] COM ports populated.
2025-07-20 16:50:47.543 +03:00 [INF] No barcode scanner detected.
2025-07-20 16:50:47.548 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 16:50:47.550 +03:00 [INF] TabPage returned successfully.
2025-07-20 16:50:48.189 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 16:50:48.203 +03:00 [INF] Total tabs in control: 5
2025-07-20 16:50:48.207 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 16:50:48.209 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 16:50:48.210 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 16:50:48.212 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 16:50:48.213 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 16:51:19.863 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 16:51:19.944 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 16:51:19.956 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 16:51:19.988 +03:00 [INF] Capture image mode set to: false
2025-07-20 16:51:19.993 +03:00 [INF] Initializing hub connection...
2025-07-20 16:51:23.030 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 16:51:23.130 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 16:51:23.138 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 16:51:23.145 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:51:23.148 +03:00 [INF] Hosting environment: Production
2025-07-20 16:51:23.150 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 16:51:23.582 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-20 16:51:23.628 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-20 16:51:23.714 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-20 16:51:23.721 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 148.193ms
2025-07-20 16:51:24.316 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 16:51:24.319 +03:00 [INF] COM ports populated.
2025-07-20 16:51:25.483 +03:00 [INF] No barcode scanner detected.
2025-07-20 16:51:25.487 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 16:51:25.489 +03:00 [INF] TabPage returned successfully.
2025-07-20 16:51:25.925 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ECIkuPzDV92xjfOlgJ4p4Q - null null
2025-07-20 16:51:25.935 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-20 16:51:26.182 +03:00 [INF] Successfully connected to Hub
2025-07-20 16:51:26.185 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-20 16:51:26.686 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 16:51:26.730 +03:00 [INF] Total tabs in control: 5
2025-07-20 16:51:26.745 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 16:51:26.748 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 16:51:26.750 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 16:51:26.753 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 16:51:26.755 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
2025-07-20 16:52:06.421 +03:00 [INF] Starting Barcode Initialization...
2025-07-20 16:52:06.545 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-20 16:52:06.555 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-20 16:52:06.613 +03:00 [INF] Capture image mode set to: false
2025-07-20 16:52:06.638 +03:00 [INF] Initializing hub connection...
2025-07-20 16:52:11.404 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-20 16:52:12.068 +03:00 [ERR] Failed to connect to Hub: No connection could be made because the target machine actively refused it. (localhost:5000)
2025-07-20 16:52:12.084 +03:00 [ERR] Hub connection failed.
2025-07-20 16:52:12.102 +03:00 [INF] Building Barcode TabPage UI.
2025-07-20 16:52:12.107 +03:00 [INF] COM ports populated.
2025-07-20 16:52:12.125 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-20 16:52:12.134 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-20 16:52:12.149 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-20 16:52:12.155 +03:00 [INF] Hosting environment: Production
2025-07-20 16:52:12.160 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-20 16:52:13.299 +03:00 [INF] No barcode scanner detected.
2025-07-20 16:52:13.305 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-20 16:52:13.310 +03:00 [INF] TabPage returned successfully.
2025-07-20 16:52:15.039 +03:00 [INF] Found 5 tab pages from plugins
2025-07-20 16:52:15.087 +03:00 [INF] Total tabs in control: 5
2025-07-20 16:52:15.104 +03:00 [INF] Tab 0: Text='إعدادات الباركود', Name='BarcodeTab', Visible=True
2025-07-20 16:52:15.107 +03:00 [INF] Tab 1: Text='إعدادات المنفذ', Name='PortTab', Visible=False
2025-07-20 16:52:15.109 +03:00 [INF] Tab 2: Text='إعدادات الطباعة', Name='PrintTab', Visible=False
2025-07-20 16:52:15.111 +03:00 [INF] Tab 3: Text='الإشعارات', Name='NotificationsTab', Visible=False
2025-07-20 16:52:15.114 +03:00 [INF] Tab 4: Text='إعدادات الماسح', Name='ScannerTab', Visible=False
